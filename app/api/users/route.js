import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/libs/next-auth";
import { prisma } from "@/libs/prisma";
import { cache, CacheKeys, checkRateLimit } from "@/libs/redis";
import { headers } from "next/headers";

// Security: Define admin user IDs or roles
const ADMIN_EMAILS = process.env.ADMIN_EMAILS?.split(',') || [];

// Security: Check if user is admin
async function isUserAdmin(session) {
  if (!session?.user?.email) return false;

  // Check if user email is in admin list
  if (ADMIN_EMAILS.includes(session.user.email)) return true;

  // Additional role-based check (if you have roles in database)
  try {
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { role: true }
    });
    return user?.role === 'ADMIN' || user?.role === 'SUPER_ADMIN';
  } catch (error) {
    console.error('Admin check error:', error);
    return false;
  }
}

// GET - Retrieve users (Admin only with strict security)
export async function GET(req) {
  try {
    // Authentication check
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized", code: "AUTH_REQUIRED" },
        { status: 401 }
      );
    }

    // Admin authorization check
    const isAdmin = await isUserAdmin(session);
    if (!isAdmin) {
      // Log unauthorized access attempt
      await prisma.auditLog.create({
        data: {
          userId: session.user.id,
          action: 'UNAUTHORIZED_USERS_ACCESS_ATTEMPT',
          details: {
            ip: headers().get('x-forwarded-for') || headers().get('x-real-ip') || 'unknown',
            userAgent: headers().get('user-agent'),
            timestamp: new Date().toISOString(),
            email: session.user.email
          }
        }
      }).catch(err => console.error('Audit log error:', err));

      return NextResponse.json(
        { error: "Forbidden", code: "INSUFFICIENT_PERMISSIONS" },
        { status: 403 }
      );
    }

    // Rate limiting for admin endpoints - 30 requests per minute
    const clientIP = headers().get('x-forwarded-for') || headers().get('x-real-ip') || 'unknown';
    const rateLimitKey = CacheKeys.rateLimit(`${session.user.id}:${clientIP}`, 'admin-users');
    const rateLimit = await checkRateLimit(rateLimitKey, 30, 60);

    if (!rateLimit.allowed) {
      return NextResponse.json(
        {
          error: "Too many requests",
          code: "RATE_LIMIT_EXCEEDED",
          retryAfter: Math.ceil((rateLimit.resetTime - Date.now()) / 1000)
        },
        { status: 429 }
      );
    }

    // Parse query parameters for pagination and filtering
    const url = new URL(req.url);
    const page = Math.max(parseInt(url.searchParams.get('page')) || 1, 1);
    const limit = Math.min(parseInt(url.searchParams.get('limit')) || 50, 100);
    const search = url.searchParams.get('search')?.trim();
    const sortBy = url.searchParams.get('sortBy') || 'createdAt';
    const sortOrder = url.searchParams.get('sortOrder') === 'asc' ? 'asc' : 'desc';
    const offset = (page - 1) * limit;

    // Build cache key
    const cacheKey = `admin_users:${page}:${limit}:${search || 'all'}:${sortBy}:${sortOrder}`;
    let result = await cache.get(cacheKey);

    if (!result) {
      // Build where clause for search
      const whereClause = search ? {
        OR: [
          { name: { contains: search, mode: 'insensitive' } },
          { email: { contains: search, mode: 'insensitive' } }
        ]
      } : {};

      // Validate sort field
      const allowedSortFields = ['createdAt', 'updatedAt', 'name', 'email', 'hasAccess'];
      const validSortBy = allowedSortFields.includes(sortBy) ? sortBy : 'createdAt';

      // Fetch users with security-conscious field selection
      const [users, totalCount] = await Promise.all([
        prisma.user.findMany({
          where: whereClause,
          select: {
            id: true,
            name: true,
            email: true,
            image: true,
            hasAccess: true,
            customerId: true,
            priceId: true,
            createdAt: true,
            updatedAt: true,
            role: true,
            // Exclude sensitive fields like apiKey, etc.
          },
          orderBy: {
            [validSortBy]: sortOrder
          },
          take: limit,
          skip: offset
        }),
        prisma.user.count({ where: whereClause })
      ]);

      result = {
        users: users,
        pagination: {
          page: page,
          limit: limit,
          total: totalCount,
          totalPages: Math.ceil(totalCount / limit),
          hasNext: offset + limit < totalCount,
          hasPrev: page > 1
        }
      };

      // Cache for 2 minutes (shorter cache for admin data)
      await cache.set(cacheKey, result, 120);
    }

    // Log admin access for audit
    await prisma.auditLog.create({
      data: {
        userId: session.user.id,
        action: 'ADMIN_USERS_ACCESS',
        details: {
          ip: clientIP,
          userAgent: headers().get('user-agent'),
          timestamp: new Date().toISOString(),
          query: { page, limit, search, sortBy, sortOrder },
          resultCount: result.users.length
        }
      }
    }).catch(err => console.error('Audit log error:', err));

    return NextResponse.json({
      success: true,
      ...result,
      meta: {
        requestedBy: session.user.email,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Admin users API error:', error);

    // Log error for security monitoring
    try {
      const session = await getServerSession(authOptions);
      if (session?.user?.id) {
        await prisma.auditLog.create({
          data: {
            userId: session.user.id,
            action: 'ADMIN_USERS_ERROR',
            details: {
              ip: headers().get('x-forwarded-for') || headers().get('x-real-ip') || 'unknown',
              userAgent: headers().get('user-agent'),
              timestamp: new Date().toISOString(),
              error: error.message
            }
          }
        });
      }
    } catch (auditError) {
      console.error('Audit log error:', auditError);
    }

    return NextResponse.json(
      {
        error: "Internal server error",
        code: "INTERNAL_ERROR",
        message: "Failed to retrieve users"
      },
      { status: 500 }
    );
  }
}
