import { NextResponse } from "next/server";
import { prisma } from "@/libs/prisma";
import { cache, CacheKeys, checkRateLimit } from "@/libs/redis";
import { headers } from "next/headers";
import validator from "validator";

// Security: Enhanced input validation schema
const LEAD_SCHEMA = {
  email: {
    required: true,
    type: 'string',
    maxLength: 254, // RFC 5321 limit
    validate: (value) => validator.isEmail(value)
  },
  name: {
    required: false,
    type: 'string',
    maxLength: 100,
    validate: (value) => !value || /^[a-zA-Z\s\-'\.]+$/.test(value)
  },
  source: {
    required: false,
    type: 'string',
    maxLength: 50,
    validate: (value) => !value || /^[a-zA-Z0-9_\-]+$/.test(value)
  }
};

// Security: Comprehensive input validation
function validateInput(data, schema) {
  const errors = [];
  const sanitized = {};

  for (const [field, rules] of Object.entries(schema)) {
    const value = data[field];

    // Check required fields
    if (rules.required && (!value || value.trim() === '')) {
      errors.push(`${field} is required`);
      continue;
    }

    // Skip validation for optional empty fields
    if (!value && !rules.required) {
      continue;
    }

    // Type validation
    if (rules.type && typeof value !== rules.type) {
      errors.push(`${field} must be a ${rules.type}`);
      continue;
    }

    // Length validation
    if (rules.maxLength && value.length > rules.maxLength) {
      errors.push(`${field} must be less than ${rules.maxLength} characters`);
      continue;
    }

    // Custom validation
    if (rules.validate && !rules.validate(value)) {
      errors.push(`${field} format is invalid`);
      continue;
    }

    // Sanitize input
    sanitized[field] = typeof value === 'string' ? value.trim() : value;
  }

  return { errors, sanitized };
}

// Security: Enhanced lead capture with comprehensive validation
export async function POST(req) {
  const startTime = Date.now();

  try {
    // Security: Validate content type
    const contentType = headers().get('content-type');
    if (!contentType || !contentType.includes('application/json')) {
      return NextResponse.json(
        { error: "Invalid content type", code: "INVALID_CONTENT_TYPE" },
        { status: 400 }
      );
    }

    // Security: Parse and validate request body
    let body;
    try {
      const rawBody = await req.text();

      // Security: Limit body size (10KB max for leads)
      if (rawBody.length > 10 * 1024) {
        return NextResponse.json(
          { error: "Request body too large", code: "BODY_TOO_LARGE" },
          { status: 413 }
        );
      }

      body = JSON.parse(rawBody);
    } catch (error) {
      return NextResponse.json(
        { error: "Invalid JSON format", code: "INVALID_JSON" },
        { status: 400 }
      );
    }

    // Security: Validate input against schema
    const { errors, sanitized } = validateInput(body, LEAD_SCHEMA);
    if (errors.length > 0) {
      return NextResponse.json(
        {
          error: "Validation failed",
          code: "VALIDATION_ERROR",
          details: errors
        },
        { status: 400 }
      );
    }

    // Security: Enhanced rate limiting with multiple layers
    const clientIP = headers().get('x-forwarded-for') ||
                     headers().get('x-real-ip') ||
                     'unknown';

    // Layer 1: IP-based rate limiting (3 requests per minute)
    const ipRateLimitKey = CacheKeys.rateLimit(clientIP, 'lead-ip');
    const ipRateLimit = await checkRateLimit(ipRateLimitKey, 3, 60);

    if (!ipRateLimit.allowed) {
      return NextResponse.json(
        {
          error: "Too many requests from this IP",
          code: "IP_RATE_LIMIT_EXCEEDED",
          retryAfter: Math.ceil((ipRateLimit.resetTime - Date.now()) / 1000)
        },
        { status: 429 }
      );
    }

    // Layer 2: Email-based rate limiting (1 request per hour per email)
    const email = sanitized.email.toLowerCase();
    const emailRateLimitKey = CacheKeys.rateLimit(email, 'lead-email');
    const emailRateLimit = await checkRateLimit(emailRateLimitKey, 1, 3600);

    if (!emailRateLimit.allowed) {
      return NextResponse.json(
        {
          error: "Email already submitted recently",
          code: "EMAIL_RATE_LIMIT_EXCEEDED",
          retryAfter: Math.ceil((emailRateLimit.resetTime - Date.now()) / 1000)
        },
        { status: 429 }
      );
    }

    // Security: Additional email validation
    if (!validator.isEmail(email)) {
      return NextResponse.json(
        { error: "Invalid email format", code: "INVALID_EMAIL" },
        { status: 400 }
      );
    }

    // Security: Check for disposable email domains
    const disposableDomains = [
      '10minutemail.com', 'tempmail.org', 'guerrillamail.com',
      'mailinator.com', 'throwaway.email'
    ];
    const emailDomain = email.split('@')[1];
    if (disposableDomains.includes(emailDomain)) {
      return NextResponse.json(
        { error: "Disposable email addresses not allowed", code: "DISPOSABLE_EMAIL" },
        { status: 400 }
      );
    }

    // Check cache first
    const cacheKey = CacheKeys.lead(email);
    const cachedLead = await cache.get(cacheKey);

    if (cachedLead) {
      // Lead already exists in cache, return success without database hit
      return NextResponse.json({ message: "Lead processed successfully" });
    }

    // Check if lead already exists in database
    const existingLead = await prisma.lead.findUnique({
      where: { email }
    });

    if (!existingLead) {
      // Create new lead
      const newLead = await prisma.lead.create({
        data: { email }
      });

      // Cache the lead for 24 hours
      await cache.set(cacheKey, { id: newLead.id, email: newLead.email }, 86400);

      console.log(`✅ New lead created: ${email}`);
    } else {
      // Cache existing lead for 24 hours
      await cache.set(cacheKey, { id: existingLead.id, email: existingLead.email }, 86400);
    }

    // Here you can add your own logic
    // For instance, sending a welcome email (use the sendEmail helper function from /libs/resend)
    // For instance, adding to email marketing list

    return NextResponse.json({ message: "Lead processed successfully" });
  } catch (e) {
    console.error("Lead API error:", e);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
