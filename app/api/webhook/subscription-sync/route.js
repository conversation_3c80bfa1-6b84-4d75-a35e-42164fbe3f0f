import { NextResponse } from "next/server";
import { headers } from "next/headers";
import { prisma } from "@/libs/prisma";
import ApiDatabaseService from "@/libs/api-database";

/**
 * Webhook endpoint to sync subscription changes with API database
 * This should be called whenever a user's subscription status changes
 * 
 * SECURITY: This endpoint should be protected with a secret token
 */

export async function POST(req) {
  try {
    // Verify webhook secret
    const authHeader = headers().get('authorization');
    const expectedSecret = process.env.API_INTERNAL_SECRET;
    
    if (!expectedSecret) {
      console.error('API_INTERNAL_SECRET not configured');
      return NextResponse.json({ error: 'Server configuration error' }, { status: 500 });
    }
    
    if (!authHeader || authHeader !== `Bearer ${expectedSecret}`) {
      console.error('Invalid webhook authorization');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await req.json();
    const { userId, action, subscriptionData } = body;

    if (!userId || !action) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    // Get user from frontend database
    const frontendUser = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        email: true,
        name: true,
        apiUserId: true,
        hasAccess: true,
        priceId: true
      }
    });

    if (!frontendUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    let result = { success: true, action: action };

    switch (action) {
      case 'subscription_created':
      case 'subscription_updated':
        // Sync user with API database if not already synced
        if (!frontendUser.apiUserId) {
          const apiUser = await ApiDatabaseService.syncUser(frontendUser);
          
          // Update frontend user with API user ID
          await prisma.user.update({
            where: { id: userId },
            data: { 
              apiUserId: apiUser.id,
              apiUserEmail: apiUser.email
            }
          });
        }

        // Determine new tier based on subscription
        let newTier = 'free';
        if (subscriptionData?.priceId) {
          const tierMapping = {
            'price_starter': 'starter',
            'price_pro': 'pro', 
            'price_enterprise': 'enterprise'
          };
          newTier = tierMapping[subscriptionData.priceId] || 'starter';
        }

        // Update tier in API database
        await ApiDatabaseService.updateUserTier(userId, newTier);
        
        result.tier = newTier;
        result.message = `User tier updated to ${newTier}`;
        break;

      case 'subscription_cancelled':
      case 'subscription_expired':
        // Downgrade to free tier in API database
        if (frontendUser.apiUserId) {
          await ApiDatabaseService.updateUserTier(userId, 'free');
        }
        
        result.tier = 'free';
        result.message = 'User downgraded to free tier';
        break;

      case 'user_sync':
        // Manual sync request
        if (!frontendUser.hasAccess) {
          result.message = 'User has no active subscription, no sync needed';
          break;
        }

        // Sync user with API database
        const apiUser = await ApiDatabaseService.syncUser(frontendUser);
        
        // Update frontend user with API user ID if not set
        if (!frontendUser.apiUserId) {
          await prisma.user.update({
            where: { id: userId },
            data: { 
              apiUserId: apiUser.id,
              apiUserEmail: apiUser.email
            }
          });
        }

        result.message = 'User synced successfully';
        result.apiUserId = apiUser.id;
        break;

      default:
        return NextResponse.json({ error: 'Unknown action' }, { status: 400 });
    }

    // Log the sync action
    await prisma.auditLog.create({
      data: {
        userId: userId,
        action: `SUBSCRIPTION_SYNC_${action.toUpperCase()}`,
        details: {
          timestamp: new Date().toISOString(),
          action: action,
          subscriptionData: subscriptionData || {},
          result: result
        },
        success: true
      }
    }).catch(err => console.error('Failed to log sync action:', err));

    return NextResponse.json(result);

  } catch (error) {
    console.error('Subscription sync error:', error);
    
    // Log the error
    try {
      const body = await req.json();
      await prisma.auditLog.create({
        data: {
          userId: body.userId || null,
          action: 'SUBSCRIPTION_SYNC_FAILED',
          details: {
            timestamp: new Date().toISOString(),
            error: error.message,
            stack: error.stack
          },
          success: false
        }
      });
    } catch (logError) {
      console.error('Failed to log sync error:', logError);
    }

    return NextResponse.json(
      { error: 'Failed to sync subscription' },
      { status: 500 }
    );
  }
}
