import { NextResponse } from "next/server";
import { headers } from "next/headers";
import <PERSON><PERSON> from "stripe";
import { prisma } from "@/libs/prisma";
import { cache, CacheKeys, checkRateLimit } from "@/libs/redis";
import configFile from "@/config";
import { findCheckoutSession } from "@/libs/stripe";
import crypto from "crypto";

// Security: Validate environment variables
if (!process.env.STRIPE_SECRET_KEY) {
  throw new Error('STRIPE_SECRET_KEY is required');
}
if (!process.env.STRIPE_WEBHOOK_SECRET) {
  throw new Error('STRIPE_WEBHOOK_SECRET is required');
}

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);
const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;

// Security: Allowed webhook event types
const ALLOWED_WEBHOOK_EVENTS = [
  'checkout.session.completed',
  'checkout.session.expired',
  'customer.subscription.updated',
  'customer.subscription.deleted',
  'invoice.paid',
  'invoice.payment_failed'
];

// Security: Validate webhook source IP (Stripe's IP ranges)
function validateStripeIP(clientIP) {
  // In production, implement proper Stripe IP validation
  // This is a simplified version
  const stripeIPRanges = [
    '**************',
    '**************',
    '*************',
    // Add more Stripe IPs as needed
  ];

  // For development, allow localhost
  if (process.env.NODE_ENV === 'development') {
    return true;
  }

  return stripeIPRanges.includes(clientIP) || clientIP.startsWith('127.0.0.1');
}

// Security: Enhanced Stripe webhook handler with comprehensive security
export async function POST(req) {
  const startTime = Date.now();

  try {
    // Security: Get client IP for validation and rate limiting
    const clientIP = headers().get('x-forwarded-for') ||
                     headers().get('x-real-ip') ||
                     req.ip ||
                     'unknown';

    // Security: Validate source IP
    if (!validateStripeIP(clientIP)) {
      console.error('Webhook from unauthorized IP:', clientIP);
      return NextResponse.json(
        { error: "Unauthorized source" },
        { status: 403 }
      );
    }

    // Security: Rate limiting for webhooks - 100 per minute per IP
    const rateLimitKey = CacheKeys.rateLimit(clientIP, 'stripe-webhook');
    const rateLimit = await checkRateLimit(rateLimitKey, 100, 60);

    if (!rateLimit.allowed) {
      console.error('Webhook rate limit exceeded for IP:', clientIP);
      return NextResponse.json(
        { error: "Rate limit exceeded" },
        { status: 429 }
      );
    }

    // Security: Validate content type
    const contentType = headers().get('content-type');
    if (!contentType || !contentType.includes('application/json')) {
      return NextResponse.json(
        { error: "Invalid content type" },
        { status: 400 }
      );
    }

    // Security: Get and validate body size
    const body = await req.text();
    if (!body || body.length === 0) {
      return NextResponse.json(
        { error: "Empty request body" },
        { status: 400 }
      );
    }

    // Security: Limit body size (1MB max)
    if (body.length > 1024 * 1024) {
      return NextResponse.json(
        { error: "Request body too large" },
        { status: 413 }
      );
    }

    // Security: Get and validate signature
    const signature = headers().get("stripe-signature");
    if (!signature) {
      return NextResponse.json(
        { error: "Missing signature" },
        { status: 400 }
      );
    }

    let event;
    let data;
    let eventType;

    // Security: Enhanced webhook signature verification
    try {
      event = stripe.webhooks.constructEvent(body, signature, webhookSecret);
      data = event.data;
      eventType = event.type;

      // Security: Validate event type
      if (!ALLOWED_WEBHOOK_EVENTS.includes(eventType)) {
        console.error('Unauthorized webhook event type:', eventType);
        return NextResponse.json(
          { error: "Event type not allowed" },
          { status: 400 }
        );
      }

      // Security: Validate event structure
      if (!event.id || !event.created || !data) {
        throw new Error('Invalid event structure');
      }

    } catch (err) {
      console.error('Webhook signature verification failed:', {
        error: err.message,
        ip: clientIP,
        timestamp: new Date().toISOString()
      });

      // Security: Log failed verification attempts
      await prisma.auditLog.create({
        data: {
          userId: null,
          action: 'WEBHOOK_VERIFICATION_FAILED',
          details: {
            ip: clientIP,
            error: err.message,
            timestamp: new Date().toISOString(),
            userAgent: headers().get('user-agent')
          }
        }
      }).catch(logErr => console.error('Failed to log audit event:', logErr));

      return NextResponse.json(
        { error: "Webhook verification failed" },
        { status: 400 }
      );
    }

  data = event.data;
  eventType = event.type;

  try {
    switch (eventType) {
      case "checkout.session.completed": {
        // First payment is successful and a subscription is created (if mode was set to "subscription" in ButtonCheckout)
        // ✅ Grant access to the product

        const session = await findCheckoutSession(data.object.id);

        const customerId = session?.customer;
        const priceId = session?.line_items?.data[0]?.price.id;
        const userId = data.object.client_reference_id;
        const plan = configFile.stripe.plans.find((p) => p.priceId === priceId);

        if (!plan) break;

        const customer = await stripe.customers.retrieve(customerId);

        let user;

        // Get or create the user. userId is normally pass in the checkout session (clientReferenceID) to identify the user when we get the webhook event
        if (userId) {
          // Check cache first
          const userCacheKey = CacheKeys.user(userId);
          user = await cache.get(userCacheKey);

          if (!user) {
            user = await prisma.user.findUnique({
              where: { id: userId }
            });

            if (user) {
              // Cache user for 1 hour
              await cache.set(userCacheKey, user, 3600);
            }
          }
        } else if (customer.email) {
          const email = customer.email.toLowerCase().trim();

          // Check cache first
          const emailCacheKey = CacheKeys.userByEmail(email);
          user = await cache.get(emailCacheKey);

          if (!user) {
            user = await prisma.user.findUnique({
              where: { email }
            });

            if (!user) {
              user = await prisma.user.create({
                data: {
                  email,
                  name: customer.name,
                }
              });
            }

            // Cache user for 1 hour
            await cache.set(emailCacheKey, user, 3600);
            await cache.set(CacheKeys.user(user.id), user, 3600);
          }
        } else {
          console.error("No user found");
          throw new Error("No user found");
        }

        // Update user data + Grant user access to your product
        const updatedUser = await prisma.user.update({
          where: { id: user.id },
          data: {
            priceId,
            customerId,
            hasAccess: true,
          }
        });

        // Update cache with new user data
        await cache.set(CacheKeys.user(user.id), updatedUser, 3600);
        await cache.set(CacheKeys.userByEmail(updatedUser.email), updatedUser, 3600);
        await cache.set(CacheKeys.userAccess(user.id), true, 3600);

        // Extra: send email with user link, product page, etc...
        // try {
        //   await sendEmail({to: ...});
        // } catch (e) {
        //   console.error("Email issue:" + e?.message);
        // }

        break;
      }

      case "checkout.session.expired": {
        // User didn't complete the transaction
        // You don't need to do anything here, by you can send an email to the user to remind him to complete the transaction, for instance
        break;
      }

      case "customer.subscription.updated": {
        // The customer might have changed the plan (higher or lower plan, cancel soon etc...)
        // You don't need to do anything here, because Stripe will let us know when the subscription is canceled for good (at the end of the billing cycle) in the "customer.subscription.deleted" event
        // You can update the user data to show a "Cancel soon" badge for instance
        break;
      }

      case "customer.subscription.deleted": {
        // The customer subscription stopped
        // ❌ Revoke access to the product
        // The customer might have changed the plan (higher or lower plan, cancel soon etc...)
        const subscription = await stripe.subscriptions.retrieve(
          data.object.id
        );

        // Find user by customerId with caching
        const customerCacheKey = CacheKeys.stripeCustomer(subscription.customer);
        let user = await cache.get(customerCacheKey);

        if (!user) {
          user = await prisma.user.findFirst({
            where: { customerId: subscription.customer }
          });

          if (user) {
            await cache.set(customerCacheKey, user, 3600);
          }
        }

        if (user) {
          // Revoke access to your product
          const updatedUser = await prisma.user.update({
            where: { id: user.id },
            data: { hasAccess: false }
          });

          // Update cache
          await cache.set(CacheKeys.user(user.id), updatedUser, 3600);
          await cache.set(CacheKeys.userAccess(user.id), false, 3600);
          await cache.del(customerCacheKey); // Remove from cache to force refresh
        }

        break;
      }

      case "invoice.paid": {
        // Customer just paid an invoice (for instance, a recurring payment for a subscription)
        // ✅ Grant access to the product
        const priceId = data.object.lines.data[0].price.id;
        const customerId = data.object.customer;

        // Find user by customerId with caching
        const customerCacheKey = CacheKeys.stripeCustomer(customerId);
        let user = await cache.get(customerCacheKey);

        if (!user) {
          user = await prisma.user.findFirst({
            where: { customerId }
          });

          if (user) {
            await cache.set(customerCacheKey, user, 3600);
          }
        }

        if (!user) break;

        // Make sure the invoice is for the same plan (priceId) the user subscribed to
        if (user.priceId !== priceId) break;

        // Grant user access to your product
        const updatedUser = await prisma.user.update({
          where: { id: user.id },
          data: { hasAccess: true }
        });

        // Update cache
        await cache.set(CacheKeys.user(user.id), updatedUser, 3600);
        await cache.set(CacheKeys.userAccess(user.id), true, 3600);

        break;
      }

      case "invoice.payment_failed":
        // A payment failed (for instance the customer does not have a valid payment method)
        // ❌ Revoke access to the product
        // ⏳ OR wait for the customer to pay (more friendly):
        //      - Stripe will automatically email the customer (Smart Retries)
        //      - We will receive a "customer.subscription.deleted" when all retries were made and the subscription has expired

        break;

      default:
      // Unhandled event type
    }
  } catch (e) {
    console.error("stripe error: " + e.message + " | EVENT TYPE: " + eventType);
  }

  return NextResponse.json({});
}
