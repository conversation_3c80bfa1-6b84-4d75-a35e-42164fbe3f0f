import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/libs/next-auth";
import { prisma } from "@/libs/prisma";
import { cache, CacheKeys, checkRateLimit } from "@/libs/redis";
import { headers } from "next/headers";
import { validateRequest, schemas } from "@/libs/validation";
import { createErrorResponse, createAuthError } from "@/libs/errors";

// GET - Security overview for authenticated users
export async function GET(req) {
  try {
    // Authentication check
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      throw createAuthError('required');
    }

    // Rate limiting - 20 requests per minute per user
    const clientIP = headers().get('x-forwarded-for') || headers().get('x-real-ip') || 'unknown';
    const rateLimitKey = CacheKeys.rateLimit(`${session.user.id}:${clientIP}`, 'security-overview');
    const rateLimit = await checkRateLimit(rateLimitKey, 20, 60);

    if (!rateLimit.allowed) {
      throw createRateLimitError('general', Math.ceil((rateLimit.resetTime - Date.now()) / 1000));
    }

    const userId = session.user.id;
    const url = new URL(req.url);
    
    // Validate query parameters
    const queryParams = {
      timeframe: url.searchParams.get('timeframe') || '24h'
    };

    // Validate timeframe
    const allowedTimeframes = ['1h', '24h', '7d'];
    if (!allowedTimeframes.includes(queryParams.timeframe)) {
      return NextResponse.json(
        { error: "Invalid timeframe", code: "INVALID_TIMEFRAME" },
        { status: 400 }
      );
    }

    // Calculate time range
    const now = new Date();
    let startTime;
    
    switch (queryParams.timeframe) {
      case '1h':
        startTime = new Date(now.getTime() - 60 * 60 * 1000);
        break;
      case '24h':
        startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        break;
      case '7d':
        startTime = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      default:
        startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    }

    // Check cache first
    const cacheKey = `security_overview:${userId}:${queryParams.timeframe}`;
    let securityData = await cache.get(cacheKey);

    if (!securityData) {
      // Fetch security metrics
      const [
        securityEvents,
        auditLogs,
        failedLogins,
        suspiciousActivity,
        apiRequests,
        userInfo
      ] = await Promise.all([
        // Security events for this user
        prisma.securityEvent.count({
          where: {
            userId: userId,
            createdAt: { gte: startTime }
          }
        }),

        // Audit logs for this user
        prisma.auditLog.count({
          where: {
            userId: userId,
            createdAt: { gte: startTime }
          }
        }),

        // Failed login attempts
        prisma.auditLog.count({
          where: {
            userId: userId,
            action: 'USER_LOGIN',
            success: false,
            createdAt: { gte: startTime }
          }
        }),

        // Suspicious activity events
        prisma.securityEvent.count({
          where: {
            userId: userId,
            type: 'SUSPICIOUS_ACTIVITY',
            createdAt: { gte: startTime }
          }
        }),

        // API requests (from audit logs)
        prisma.auditLog.count({
          where: {
            userId: userId,
            action: { startsWith: 'API_' },
            createdAt: { gte: startTime }
          }
        }),

        // User security info
        prisma.user.findUnique({
          where: { id: userId },
          select: {
            lastLoginAt: true,
            loginAttempts: true,
            lockedUntil: true,
            twoFactorEnabled: true,
            apiKeyLastUsed: true,
            apiKeyUsageCount: true
          }
        })
      ]);

      // Calculate security score
      let securityScore = 100;
      
      // Deduct points for security issues
      if (securityEvents > 0) securityScore -= Math.min(securityEvents * 5, 30);
      if (failedLogins > 5) securityScore -= Math.min((failedLogins - 5) * 2, 20);
      if (suspiciousActivity > 0) securityScore -= Math.min(suspiciousActivity * 10, 40);
      if (userInfo?.loginAttempts > 0) securityScore -= Math.min(userInfo.loginAttempts * 3, 15);
      
      // Bonus points for security features
      if (userInfo?.twoFactorEnabled) securityScore += 5;
      
      securityScore = Math.max(Math.min(securityScore, 100), 0);

      // Get recent security events for details
      const recentEvents = await prisma.securityEvent.findMany({
        where: {
          userId: userId,
          createdAt: { gte: startTime }
        },
        orderBy: { createdAt: 'desc' },
        take: 5,
        select: {
          id: true,
          type: true,
          severity: true,
          description: true,
          createdAt: true
        }
      });

      // Get recent audit logs
      const recentAuditLogs = await prisma.auditLog.findMany({
        where: {
          userId: userId,
          createdAt: { gte: startTime }
        },
        orderBy: { createdAt: 'desc' },
        take: 10,
        select: {
          id: true,
          action: true,
          success: true,
          createdAt: true,
          ipAddress: true
        }
      });

      // Compile security data
      securityData = {
        timeframe: queryParams.timeframe,
        securityScore: Math.round(securityScore),
        metrics: {
          securityEvents,
          auditLogs,
          failedLogins,
          suspiciousActivity,
          apiRequests
        },
        userSecurity: {
          lastLoginAt: userInfo?.lastLoginAt,
          loginAttempts: userInfo?.loginAttempts || 0,
          isLocked: userInfo?.lockedUntil && userInfo.lockedUntil > new Date(),
          twoFactorEnabled: userInfo?.twoFactorEnabled || false,
          apiKeyLastUsed: userInfo?.apiKeyLastUsed,
          apiKeyUsageCount: userInfo?.apiKeyUsageCount || 0
        },
        recentEvents: recentEvents.map(event => ({
          id: event.id,
          type: event.type,
          severity: event.severity,
          description: event.description,
          timestamp: event.createdAt
        })),
        recentActivity: recentAuditLogs.map(log => ({
          id: log.id,
          action: log.action,
          success: log.success,
          timestamp: log.createdAt,
          // Hash IP for privacy
          ipHash: log.ipAddress ? 
            require('crypto').createHash('sha256').update(log.ipAddress).digest('hex').substring(0, 8) : 
            null
        })),
        generatedAt: new Date().toISOString()
      };

      // Cache for 5 minutes
      await cache.set(cacheKey, securityData, 300);
    }

    // Log access for audit
    await prisma.auditLog.create({
      data: {
        userId: userId,
        action: 'SECURITY_OVERVIEW_ACCESSED',
        details: {
          timeframe: queryParams.timeframe,
          timestamp: new Date().toISOString()
        },
        ipAddress: clientIP,
        userAgent: headers().get('user-agent')
      }
    }).catch(err => console.error('Failed to log audit event:', err));

    return NextResponse.json({
      success: true,
      data: securityData
    });

  } catch (error) {
    console.error('Security overview error:', error);
    return await createErrorResponse(error, req, session?.user?.id);
  }
}
