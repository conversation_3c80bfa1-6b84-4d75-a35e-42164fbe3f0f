import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/libs/next-auth";
import { prisma } from "@/libs/prisma";
import { checkRateLimit, Cache<PERSON>eys } from "@/libs/redis";
import { headers } from "next/headers";

// POST - Log API key audit events
export async function POST(req) {
  try {
    // Authentication check
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized", code: "AUTH_REQUIRED" },
        { status: 401 }
      );
    }

    // Rate limiting - 20 audit logs per minute per user
    const clientIP = headers().get('x-forwarded-for') || headers().get('x-real-ip') || 'unknown';
    const rateLimitKey = CacheKeys.rateLimit(`${session.user.id}:${clientIP}`, 'audit-log');
    const rateLimit = await checkRateLimit(rateLimitKey, 20, 60);

    if (!rateLimit.allowed) {
      return NextResponse.json(
        { 
          error: "Too many requests", 
          code: "RATE_LIMIT_EXCEEDED",
          retryAfter: Math.ceil((rateLimit.resetTime - Date.now()) / 1000)
        },
        { status: 429 }
      );
    }

    // Parse and validate request body
    const body = await req.json();
    const { action, timestamp } = body;

    // Input validation
    if (!action || typeof action !== 'string') {
      return NextResponse.json(
        { error: "Invalid action parameter", code: "INVALID_INPUT" },
        { status: 400 }
      );
    }

    // Validate action type
    const allowedActions = ['copy', 'view', 'regenerate', 'revoke'];
    if (!allowedActions.includes(action.toLowerCase())) {
      return NextResponse.json(
        { error: "Invalid action type", code: "INVALID_ACTION" },
        { status: 400 }
      );
    }

    // Validate timestamp
    const auditTimestamp = timestamp ? new Date(timestamp) : new Date();
    if (isNaN(auditTimestamp.getTime())) {
      return NextResponse.json(
        { error: "Invalid timestamp", code: "INVALID_TIMESTAMP" },
        { status: 400 }
      );
    }

    const userId = session.user.id;

    // Create audit log entry
    await prisma.auditLog.create({
      data: {
        userId: userId,
        action: `API_KEY_${action.toUpperCase()}`,
        details: {
          ip: clientIP,
          userAgent: headers().get('user-agent'),
          timestamp: auditTimestamp.toISOString(),
          sessionId: session.user.id // Could be enhanced with actual session ID
        }
      }
    });

    return NextResponse.json({
      success: true,
      message: "Audit event logged successfully"
    });

  } catch (error) {
    console.error('Audit logging error:', error);
    return NextResponse.json(
      { 
        error: "Internal server error", 
        code: "INTERNAL_ERROR",
        message: "Failed to log audit event"
      },
      { status: 500 }
    );
  }
}

// GET - Retrieve audit logs for the user
export async function GET(req) {
  try {
    // Authentication check
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized", code: "AUTH_REQUIRED" },
        { status: 401 }
      );
    }

    // Rate limiting - 10 requests per minute per user
    const clientIP = headers().get('x-forwarded-for') || headers().get('x-real-ip') || 'unknown';
    const rateLimitKey = CacheKeys.rateLimit(`${session.user.id}:${clientIP}`, 'audit-get');
    const rateLimit = await checkRateLimit(rateLimitKey, 10, 60);

    if (!rateLimit.allowed) {
      return NextResponse.json(
        { 
          error: "Too many requests", 
          code: "RATE_LIMIT_EXCEEDED",
          retryAfter: Math.ceil((rateLimit.resetTime - Date.now()) / 1000)
        },
        { status: 429 }
      );
    }

    const userId = session.user.id;
    const url = new URL(req.url);
    const limit = Math.min(parseInt(url.searchParams.get('limit')) || 50, 100);
    const offset = Math.max(parseInt(url.searchParams.get('offset')) || 0, 0);

    // Retrieve audit logs for the user
    const auditLogs = await prisma.auditLog.findMany({
      where: {
        userId: userId,
        action: {
          startsWith: 'API_KEY_'
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: limit,
      skip: offset,
      select: {
        id: true,
        action: true,
        createdAt: true,
        details: true
      }
    });

    // Get total count for pagination
    const totalCount = await prisma.auditLog.count({
      where: {
        userId: userId,
        action: {
          startsWith: 'API_KEY_'
        }
      }
    });

    // Sanitize sensitive information from details
    const sanitizedLogs = auditLogs.map(log => ({
      ...log,
      details: {
        timestamp: log.details?.timestamp,
        action: log.action,
        // Remove sensitive info like full IP, user agent
        ipHash: log.details?.ip ? 
          require('crypto').createHash('sha256').update(log.details.ip).digest('hex').substring(0, 8) : 
          null
      }
    }));

    return NextResponse.json({
      success: true,
      logs: sanitizedLogs,
      pagination: {
        total: totalCount,
        limit: limit,
        offset: offset,
        hasMore: offset + limit < totalCount
      }
    });

  } catch (error) {
    console.error('Audit log retrieval error:', error);
    return NextResponse.json(
      { 
        error: "Internal server error", 
        code: "INTERNAL_ERROR",
        message: "Failed to retrieve audit logs"
      },
      { status: 500 }
    );
  }
}
