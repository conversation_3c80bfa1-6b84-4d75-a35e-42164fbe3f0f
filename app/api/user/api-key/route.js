import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/libs/next-auth";
import { headers } from "next/headers";
import { prisma } from "@/libs/prisma";
import ApiDatabaseService from "@/libs/api-database";
import { createAuthError, createRateLimitError } from "@/libs/errors";
import { checkRateLimit } from "@/libs/rate-limit";
import { CacheKeys } from "@/libs/cache";

// GET - Fetch user's API key information from API database
export async function GET(req) {
  try {
    // Authentication check
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      throw createAuthError('required');
    }

    // Rate limiting - 10 requests per minute per user
    const clientIP = headers().get('x-forwarded-for') || headers().get('x-real-ip') || 'unknown';
    const rateLimitKey = CacheKeys.rateLimit(`${session.user.id}:${clientIP}`, 'api-key-fetch');
    const rateLimit = await checkRateLimit(rateLimitKey, 10, 60);

    if (!rateLimit.allowed) {
      throw createRateLimitError('general', Math.ceil((rateLimit.resetTime - Date.now()) / 1000));
    }

    const frontendUserId = session.user.id;

    // Get user from frontend database
    const frontendUser = await prisma.user.findUnique({
      where: { id: frontendUserId },
      select: {
        id: true,
        email: true,
        name: true,
        apiUserId: true,
        hasAccess: true
      }
    });

    if (!frontendUser) {
      throw createAuthError('user_not_found');
    }

    // Check if user has access (paid subscription)
    if (!frontendUser.hasAccess) {
      return NextResponse.json({
        success: false,
        error: 'API access requires an active subscription',
        hasAccess: false
      }, { status: 403 });
    }

    // Sync user with API database if not already synced
    let apiUser;
    if (!frontendUser.apiUserId) {
      apiUser = await ApiDatabaseService.syncUser(frontendUser);
      
      // Update frontend user with API user ID
      await prisma.user.update({
        where: { id: frontendUserId },
        data: { 
          apiUserId: apiUser.id,
          apiUserEmail: apiUser.email
        }
      });
    }

    // Get API key information from API database
    const apiKeyInfo = await ApiDatabaseService.getUserApiKey(frontendUserId);

    // Log access for audit (in frontend database)
    await prisma.auditLog.create({
      data: {
        userId: frontendUserId,
        action: 'API_KEY_INFO_ACCESSED',
        details: {
          timestamp: new Date().toISOString(),
          hasApiKey: apiKeyInfo.hasApiKey
        },
        ipAddress: clientIP,
        userAgent: headers().get('user-agent'),
        success: true
      }
    }).catch(err => console.error('Failed to log audit event:', err));

    return NextResponse.json({
      success: true,
      ...apiKeyInfo
    });

  } catch (error) {
    console.error('API key fetch error:', error);
    
    if (error.name === 'AuthError' || error.name === 'RateLimitError') {
      return NextResponse.json(
        { error: error.message },
        { status: error.statusCode }
      );
    }

    return NextResponse.json(
      { error: 'Failed to fetch API key information' },
      { status: 500 }
    );
  }
}

// POST - Generate new API key in API database
export async function POST(req) {
  try {
    // Authentication check
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      throw createAuthError('required');
    }

    // Rate limiting - 3 generations per hour per user
    const clientIP = headers().get('x-forwarded-for') || headers().get('x-real-ip') || 'unknown';
    const rateLimitKey = CacheKeys.rateLimit(`${session.user.id}:${clientIP}`, 'api-key-generate');
    const rateLimit = await checkRateLimit(rateLimitKey, 3, 3600); // 3 per hour

    if (!rateLimit.allowed) {
      throw createRateLimitError('api_key_generation', Math.ceil((rateLimit.resetTime - Date.now()) / 1000));
    }

    const frontendUserId = session.user.id;

    // Get user from frontend database
    const frontendUser = await prisma.user.findUnique({
      where: { id: frontendUserId },
      select: {
        id: true,
        email: true,
        name: true,
        apiUserId: true,
        hasAccess: true
      }
    });

    if (!frontendUser) {
      throw createAuthError('user_not_found');
    }

    // Check if user has access (paid subscription)
    if (!frontendUser.hasAccess) {
      return NextResponse.json({
        success: false,
        error: 'API key generation requires an active subscription',
        hasAccess: false
      }, { status: 403 });
    }

    // Sync user with API database if not already synced
    if (!frontendUser.apiUserId) {
      const apiUser = await ApiDatabaseService.syncUser(frontendUser);
      
      // Update frontend user with API user ID
      await prisma.user.update({
        where: { id: frontendUserId },
        data: { 
          apiUserId: apiUser.id,
          apiUserEmail: apiUser.email
        }
      });
    }

    // Generate new API key in API database
    const newApiKeyInfo = await ApiDatabaseService.generateApiKey(frontendUserId);

    // Log generation for audit (in frontend database)
    await prisma.auditLog.create({
      data: {
        userId: frontendUserId,
        action: 'API_KEY_GENERATED',
        details: {
          timestamp: new Date().toISOString(),
          keyPreview: newApiKeyInfo.keyPreview
        },
        ipAddress: clientIP,
        userAgent: headers().get('user-agent'),
        success: true
      }
    }).catch(err => console.error('Failed to log audit event:', err));

    return NextResponse.json({
      success: true,
      message: 'New API key generated successfully',
      ...newApiKeyInfo
    });

  } catch (error) {
    console.error('API key generation error:', error);
    
    // Log failed generation attempt
    try {
      await prisma.auditLog.create({
        data: {
          userId: session?.user?.id,
          action: 'API_KEY_GENERATION_FAILED',
          details: {
            timestamp: new Date().toISOString(),
            error: error.message
          },
          ipAddress: headers().get('x-forwarded-for') || 'unknown',
          userAgent: headers().get('user-agent'),
          success: false
        }
      });
    } catch (logError) {
      console.error('Failed to log failed generation:', logError);
    }
    
    if (error.name === 'AuthError' || error.name === 'RateLimitError') {
      return NextResponse.json(
        { error: error.message },
        { status: error.statusCode }
      );
    }

    return NextResponse.json(
      { error: 'Failed to generate API key' },
      { status: 500 }
    );
  }
}
