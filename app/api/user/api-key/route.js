import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/libs/next-auth";
import { prisma } from "@/libs/prisma";
import { cache, CacheKeys, checkRateLimit } from "@/libs/redis";
import crypto from "crypto";
import { headers } from "next/headers";

// Security: Generate cryptographically secure API key
function generateSecureApiKey() {
  const prefix = "sk_live_";
  const randomBytes = crypto.randomBytes(32);
  const key = randomBytes.toString('hex');
  return `${prefix}${key}`;
}

// Security: Hash API key for storage
function hashApiKey(apiKey) {
  return crypto.createHash('sha256').update(apiKey).digest('hex');
}

// Security: Validate API key format
function validateApiKeyFormat(apiKey) {
  const pattern = /^sk_live_[a-f0-9]{64}$/;
  return pattern.test(apiKey);
}

// GET - Retrieve user's API key information (masked)
export async function GET(req) {
  try {
    // Authentication check
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized", code: "AUTH_REQUIRED" },
        { status: 401 }
      );
    }

    // Rate limiting - 10 requests per minute per user
    const clientIP = headers().get('x-forwarded-for') || headers().get('x-real-ip') || 'unknown';
    const rateLimitKey = CacheKeys.rateLimit(`${session.user.id}:${clientIP}`, 'api-key-get');
    const rateLimit = await checkRateLimit(rateLimitKey, 10, 60);

    if (!rateLimit.allowed) {
      return NextResponse.json(
        { 
          error: "Too many requests", 
          code: "RATE_LIMIT_EXCEEDED",
          retryAfter: Math.ceil((rateLimit.resetTime - Date.now()) / 1000)
        },
        { status: 429 }
      );
    }

    const userId = session.user.id;

    // Check cache first
    const userCacheKey = CacheKeys.user(userId);
    let user = await cache.get(userCacheKey);

    if (!user) {
      user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          apiKey: true,
          apiKeyLastUsed: true,
          apiKeyUsageCount: true,
          apiKeyCreatedAt: true
        }
      });

      if (!user) {
        return NextResponse.json(
          { error: "User not found", code: "USER_NOT_FOUND" },
          { status: 404 }
        );
      }

      // Cache user for 5 minutes
      await cache.set(userCacheKey, user, 300);
    }

    // Generate API key if user doesn't have one
    if (!user.apiKey) {
      const newApiKey = generateSecureApiKey();
      const hashedApiKey = hashApiKey(newApiKey);

      user = await prisma.user.update({
        where: { id: userId },
        data: {
          apiKey: hashedApiKey,
          apiKeyCreatedAt: new Date(),
          apiKeyUsageCount: 0
        },
        select: {
          id: true,
          apiKey: true,
          apiKeyLastUsed: true,
          apiKeyUsageCount: true,
          apiKeyCreatedAt: true
        }
      });

      // Update cache
      await cache.set(userCacheKey, user, 300);

      // Return the plain API key only once during creation
      return NextResponse.json({
        success: true,
        apiKey: newApiKey,
        lastUsed: user.apiKeyLastUsed,
        usageCount: user.apiKeyUsageCount || 0,
        createdAt: user.apiKeyCreatedAt,
        isNew: true
      });
    }

    // For existing keys, we can't return the plain key (it's hashed)
    // Client will need to use the stored key or regenerate
    return NextResponse.json({
      success: true,
      hasApiKey: true,
      lastUsed: user.apiKeyLastUsed,
      usageCount: user.apiKeyUsageCount || 0,
      createdAt: user.apiKeyCreatedAt,
      message: "API key exists but cannot be retrieved for security reasons"
    });

  } catch (error) {
    console.error('API key retrieval error:', error);
    return NextResponse.json(
      { 
        error: "Internal server error", 
        code: "INTERNAL_ERROR",
        message: "Failed to retrieve API key information"
      },
      { status: 500 }
    );
  }
}

// POST - Create or regenerate API key
export async function POST(req) {
  try {
    // Authentication check
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized", code: "AUTH_REQUIRED" },
        { status: 401 }
      );
    }

    // Rate limiting - 3 regenerations per hour per user
    const clientIP = headers().get('x-forwarded-for') || headers().get('x-real-ip') || 'unknown';
    const rateLimitKey = CacheKeys.rateLimit(`${session.user.id}:${clientIP}`, 'api-key-regen');
    const rateLimit = await checkRateLimit(rateLimitKey, 3, 3600);

    if (!rateLimit.allowed) {
      return NextResponse.json(
        { 
          error: "Too many regeneration attempts", 
          code: "RATE_LIMIT_EXCEEDED",
          retryAfter: Math.ceil((rateLimit.resetTime - Date.now()) / 1000)
        },
        { status: 429 }
      );
    }

    const userId = session.user.id;

    // Generate new secure API key
    const newApiKey = generateSecureApiKey();
    const hashedApiKey = hashApiKey(newApiKey);

    // Update user with new API key
    const user = await prisma.user.update({
      where: { id: userId },
      data: {
        apiKey: hashedApiKey,
        apiKeyCreatedAt: new Date(),
        apiKeyLastUsed: null,
        apiKeyUsageCount: 0
      },
      select: {
        id: true,
        apiKey: true,
        apiKeyLastUsed: true,
        apiKeyUsageCount: true,
        apiKeyCreatedAt: true
      }
    });

    // Update cache
    const userCacheKey = CacheKeys.user(userId);
    await cache.set(userCacheKey, user, 300);

    // Log the regeneration for security audit
    await prisma.auditLog.create({
      data: {
        userId: userId,
        action: 'API_KEY_REGENERATED',
        details: {
          ip: clientIP,
          userAgent: headers().get('user-agent'),
          timestamp: new Date().toISOString()
        }
      }
    }).catch(err => {
      console.error('Failed to log audit event:', err);
      // Don't fail the request if audit logging fails
    });

    return NextResponse.json({
      success: true,
      apiKey: newApiKey,
      lastUsed: user.apiKeyLastUsed,
      usageCount: user.apiKeyUsageCount || 0,
      createdAt: user.apiKeyCreatedAt,
      message: "API key regenerated successfully"
    });

  } catch (error) {
    console.error('API key regeneration error:', error);
    return NextResponse.json(
      { 
        error: "Internal server error", 
        code: "INTERNAL_ERROR",
        message: "Failed to regenerate API key"
      },
      { status: 500 }
    );
  }
}

// DELETE - Revoke API key
export async function DELETE(req) {
  try {
    // Authentication check
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized", code: "AUTH_REQUIRED" },
        { status: 401 }
      );
    }

    const userId = session.user.id;

    // Revoke API key
    await prisma.user.update({
      where: { id: userId },
      data: {
        apiKey: null,
        apiKeyLastUsed: null,
        apiKeyUsageCount: 0,
        apiKeyCreatedAt: null
      }
    });

    // Clear cache
    const userCacheKey = CacheKeys.user(userId);
    await cache.del(userCacheKey);

    // Log the revocation for security audit
    const clientIP = headers().get('x-forwarded-for') || headers().get('x-real-ip') || 'unknown';
    await prisma.auditLog.create({
      data: {
        userId: userId,
        action: 'API_KEY_REVOKED',
        details: {
          ip: clientIP,
          userAgent: headers().get('user-agent'),
          timestamp: new Date().toISOString()
        }
      }
    }).catch(err => {
      console.error('Failed to log audit event:', err);
    });

    return NextResponse.json({
      success: true,
      message: "API key revoked successfully"
    });

  } catch (error) {
    console.error('API key revocation error:', error);
    return NextResponse.json(
      { 
        error: "Internal server error", 
        code: "INTERNAL_ERROR",
        message: "Failed to revoke API key"
      },
      { status: 500 }
    );
  }
}
