import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/libs/next-auth";
import { prisma } from "@/libs/prisma";
import { cache, CacheKeys, checkRateLimit } from "@/libs/redis";
import crypto from "crypto";
import { headers } from "next/headers";

// Security: Generate cryptographically secure API key
function generateSecureApiKey() {
  const prefix = "sk_live_";
  const randomBytes = crypto.randomBytes(32);
  const key = randomBytes.toString('hex');
  return `${prefix}${key}`;
}

// Security: Hash API key for storage
function hashApiKey(apiKey) {
  return crypto.createHash('sha256').update(apiKey).digest('hex');
}

// POST - Regenerate API key with enhanced security
export async function POST(req) {
  try {
    // Authentication check
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized", code: "AUTH_REQUIRED" },
        { status: 401 }
      );
    }

    // Enhanced rate limiting - 2 regenerations per hour per user
    const clientIP = headers().get('x-forwarded-for') || headers().get('x-real-ip') || 'unknown';
    const rateLimitKey = CacheKeys.rateLimit(`${session.user.id}:${clientIP}`, 'api-key-regen');
    const rateLimit = await checkRateLimit(rateLimitKey, 2, 3600);

    if (!rateLimit.allowed) {
      return NextResponse.json(
        { 
          error: "Too many regeneration attempts", 
          code: "RATE_LIMIT_EXCEEDED",
          retryAfter: Math.ceil((rateLimit.resetTime - Date.now()) / 1000),
          message: "API key regeneration is limited to 2 times per hour for security"
        },
        { status: 429 }
      );
    }

    const userId = session.user.id;

    // Parse request body for additional security validation
    let body = {};
    try {
      body = await req.json();
    } catch (error) {
      // Body is optional for regeneration
    }

    // Optional: Require confirmation token for additional security
    if (body.requireConfirmation && !body.confirmationToken) {
      return NextResponse.json(
        { error: "Confirmation token required", code: "CONFIRMATION_REQUIRED" },
        { status: 400 }
      );
    }

    // Get current user data
    const currentUser = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        email: true,
        apiKey: true,
        apiKeyCreatedAt: true,
        apiKeyUsageCount: true
      }
    });

    if (!currentUser) {
      return NextResponse.json(
        { error: "User not found", code: "USER_NOT_FOUND" },
        { status: 404 }
      );
    }

    // Generate new secure API key
    const newApiKey = generateSecureApiKey();
    const hashedApiKey = hashApiKey(newApiKey);

    // Store old API key info for audit
    const oldApiKeyInfo = {
      existed: !!currentUser.apiKey,
      createdAt: currentUser.apiKeyCreatedAt,
      usageCount: currentUser.apiKeyUsageCount || 0
    };

    // Update user with new API key in a transaction
    const updatedUser = await prisma.$transaction(async (tx) => {
      // Update user with new API key
      const user = await tx.user.update({
        where: { id: userId },
        data: {
          apiKey: hashedApiKey,
          apiKeyCreatedAt: new Date(),
          apiKeyLastUsed: null,
          apiKeyUsageCount: 0
        },
        select: {
          id: true,
          apiKey: true,
          apiKeyLastUsed: true,
          apiKeyUsageCount: true,
          apiKeyCreatedAt: true
        }
      });

      // Create audit log entry
      await tx.auditLog.create({
        data: {
          userId: userId,
          action: 'API_KEY_REGENERATED',
          details: {
            ip: clientIP,
            userAgent: headers().get('user-agent'),
            timestamp: new Date().toISOString(),
            oldKeyInfo: oldApiKeyInfo,
            reason: body.reason || 'User requested regeneration'
          }
        }
      });

      return user;
    });

    // Update cache
    const userCacheKey = CacheKeys.user(userId);
    await cache.set(userCacheKey, updatedUser, 300);

    // Clear any cached API key validations
    await cache.del(`api_key_validation:${userId}`);

    // Optional: Send security notification email
    if (body.notifyEmail !== false) {
      try {
        // Import email service
        const { sendEmail } = await import('@/libs/email');
        
        await sendEmail({
          to: currentUser.email,
          subject: 'API Key Regenerated - StalkAPI',
          text: `Your API key has been regenerated. If this wasn't you, please contact support immediately.`,
          html: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
              <h2 style="color: #dc2626;">🔐 API Key Regenerated</h2>
              <p>Your API key has been successfully regenerated.</p>
              <div style="background-color: #fef2f2; border: 1px solid #fecaca; padding: 16px; border-radius: 8px; margin: 16px 0;">
                <p><strong>Time:</strong> ${new Date().toLocaleString()}</p>
                <p><strong>IP Address:</strong> ${clientIP}</p>
              </div>
              <p>If this wasn't you, please contact our support team immediately.</p>
              <p style="color: #666; font-size: 12px;">This is an automated security notification.</p>
            </div>
          `
        });
      } catch (emailError) {
        console.error('Failed to send security notification email:', emailError);
        // Don't fail the request if email fails
      }
    }

    return NextResponse.json({
      success: true,
      apiKey: newApiKey,
      lastUsed: updatedUser.apiKeyLastUsed,
      usageCount: updatedUser.apiKeyUsageCount || 0,
      createdAt: updatedUser.apiKeyCreatedAt,
      message: "API key regenerated successfully",
      security: {
        previousKeyRevoked: oldApiKeyInfo.existed,
        notificationSent: body.notifyEmail !== false
      }
    });

  } catch (error) {
    console.error('API key regeneration error:', error);
    
    // Log security event for failed regeneration attempts
    try {
      const session = await getServerSession(authOptions);
      if (session?.user?.id) {
        await prisma.auditLog.create({
          data: {
            userId: session.user.id,
            action: 'API_KEY_REGENERATION_FAILED',
            details: {
              ip: headers().get('x-forwarded-for') || headers().get('x-real-ip') || 'unknown',
              userAgent: headers().get('user-agent'),
              timestamp: new Date().toISOString(),
              error: error.message
            }
          }
        });
      }
    } catch (auditError) {
      console.error('Failed to log audit event:', auditError);
    }

    return NextResponse.json(
      { 
        error: "Internal server error", 
        code: "INTERNAL_ERROR",
        message: "Failed to regenerate API key"
      },
      { status: 500 }
    );
  }
}
