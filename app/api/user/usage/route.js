import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/libs/next-auth";
import { headers } from "next/headers";
import { prisma } from "@/libs/prisma";
import ApiDatabaseService from "@/libs/api-database";
import { createAuthError, createRateLimitError } from "@/libs/errors";
import { checkRateLimit } from "@/libs/rate-limit";
import { CacheKeys } from "@/libs/cache";

// GET - Fetch user's API usage statistics from API database
export async function GET(req) {
  try {
    // Authentication check
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      throw createAuthError('required');
    }

    // Rate limiting - 20 requests per minute per user
    const clientIP = headers().get('x-forwarded-for') || headers().get('x-real-ip') || 'unknown';
    const rateLimitKey = CacheKeys.rateLimit(`${session.user.id}:${clientIP}`, 'usage-stats');
    const rateLimit = await checkRateLimit(rateLimitKey, 20, 60);

    if (!rateLimit.allowed) {
      throw createRateLimitError('general', Math.ceil((rateLimit.resetTime - Date.now()) / 1000));
    }

    const frontendUserId = session.user.id;

    // Get user from frontend database
    const frontendUser = await prisma.user.findUnique({
      where: { id: frontendUserId },
      select: {
        id: true,
        email: true,
        name: true,
        apiUserId: true,
        hasAccess: true,
        priceId: true
      }
    });

    if (!frontendUser) {
      throw createAuthError('user_not_found');
    }

    // Check if user has access (paid subscription)
    if (!frontendUser.hasAccess) {
      return NextResponse.json({
        success: true,
        hasAccess: false,
        creditsUsed: 0,
        creditsRemaining: 0,
        totalRequests: 0,
        tier: 'free',
        message: 'Subscribe to access API usage statistics'
      });
    }

    // Get usage statistics from API database
    const usageStats = await ApiDatabaseService.getUserUsageStats(frontendUserId);

    // Determine tier based on Stripe price ID
    let tier = 'free';
    if (frontendUser.priceId) {
      // Map Stripe price IDs to tiers (you'll need to update these based on your actual price IDs)
      const tierMapping = {
        'price_starter': 'starter',
        'price_pro': 'pro',
        'price_enterprise': 'enterprise'
      };
      tier = tierMapping[frontendUser.priceId] || 'starter';
    }

    // Get tier limits (you can move this to a config file)
    const tierLimits = {
      free: { credits: 0, requests: 0 },
      starter: { credits: 1000, requests: 10000 },
      pro: { credits: 10000, requests: 100000 },
      enterprise: { credits: 100000, requests: 1000000 }
    };

    const limits = tierLimits[tier] || tierLimits.free;

    return NextResponse.json({
      success: true,
      hasAccess: true,
      tier: tier,
      creditsUsed: usageStats.creditsUsed,
      creditsRemaining: Math.max(0, limits.credits - usageStats.creditsUsed),
      creditsLimit: limits.credits,
      totalRequests: usageStats.totalRequests,
      requestsLimit: limits.requests,
      lastUsed: usageStats.lastUsed,
      currentPeriod: {
        start: new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString(),
        end: new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0).toISOString()
      }
    });

  } catch (error) {
    console.error('Usage stats fetch error:', error);
    
    if (error.name === 'AuthError' || error.name === 'RateLimitError') {
      return NextResponse.json(
        { error: error.message },
        { status: error.statusCode }
      );
    }

    return NextResponse.json(
      { error: 'Failed to fetch usage statistics' },
      { status: 500 }
    );
  }
}
