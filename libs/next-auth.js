import GoogleProvider from "next-auth/providers/google";
import EmailProvider from "next-auth/providers/email";
import { PrismaAdapter } from "@auth/prisma-adapter";
import config from "@/config";
import { prisma } from "./prisma";

export const authOptions = {
  // Security: Enhanced secret configuration
  secret: process.env.NEXTAUTH_SECRET,

  // Security: Enhanced session configuration
  session: {
    strategy: "jwt",
    maxAge: 24 * 60 * 60, // 24 hours
    updateAge: 60 * 60, // 1 hour
  },

  // Security: JWT configuration - simplified for development
  jwt: {
    maxAge: 24 * 60 * 60, // 24 hours
  },

  // Simplified cookie configuration
  cookies: {
    sessionToken: {
      name: `next-auth.session-token`,
      options: {
        httpOnly: true,
        sameSite: 'lax',
        path: '/',
        secure: process.env.NODE_ENV === 'production',
      }
    }
  },

  providers: [
    // Google OAuth provider - conditionally enabled
    ...(config.enabledAuth.google && process.env.GOOGLE_ID && process.env.GOOGLE_SECRET
      ? [
          GoogleProvider({
            // Follow the "Login with Google" tutorial to get your credentials
            clientId: process.env.GOOGLE_ID,
            clientSecret: process.env.GOOGLE_SECRET,
            async profile(profile) {
              return {
                id: profile.sub,
                name: profile.given_name ? profile.given_name : profile.name,
                email: profile.email,
                image: profile.picture,
                createdAt: new Date(),
              };
            },
          }),
        ]
      : []),
    // Email provider with magic links using your SMTP server
    // Requires a PostgreSQL database adapter to store verification tokens
    ...(config.enabledAuth.email && process.env.POSTGRE_DB && process.env.SMTP_HOST
      ? [
          EmailProvider({
            server: {
              host: process.env.SMTP_HOST,
              port: parseInt(process.env.SMTP_PORT) || 587,
              secure: process.env.SMTP_SECURE === 'true', // true for 465, false for other ports
              auth: {
                user: process.env.SMTP_USER,
                pass: process.env.SMTP_PASSWORD,
              },
              // Additional security options
              tls: {
                rejectUnauthorized: process.env.SMTP_REJECT_UNAUTHORIZED !== 'false'
              }
            },
            from: process.env.SMTP_FROM || config.resend.fromNoReply,
            // Security: Enhanced magic link configuration
            maxAge: 15 * 60, // 15 minutes for security
            // Security: Custom secure email template
            sendVerificationRequest: async ({ identifier: email, url, provider }) => {
              const { createTransport } = await import('nodemailer');
              const crypto = await import('crypto');

              // Security: Validate email format
              const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
              if (!emailRegex.test(email)) {
                throw new Error('Invalid email format');
              }

              // Security: Validate URL
              try {
                const urlObj = new URL(url);
                if (!urlObj.hostname.includes(config.domainName)) {
                  throw new Error('Invalid callback URL');
                }
              } catch (error) {
                throw new Error('Invalid callback URL');
              }

              const transport = createTransport(provider.server);

              // Security: Sanitize inputs for HTML
              const sanitizedEmail = email.replace(/[<>]/g, '');
              const sanitizedAppName = config.appName.replace(/[<>]/g, '');

              const result = await transport.sendMail({
                to: sanitizedEmail,
                from: provider.from,
                subject: `🔐 Secure Sign-in to ${sanitizedAppName}`,
                text: `Secure Sign-in to ${sanitizedAppName}\n\nClick the link below to sign in securely:\n${url}\n\nThis link will expire in 15 minutes for your security.\n\nIf you did not request this email, please ignore it.\n\nSecurity Notice: Never share this link with anyone.`,
                html: `
                  <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
                    <h2 style="color: #333;">🔐 Secure Sign-in to ${sanitizedAppName}</h2>
                    <p>Click the button below to sign in securely:</p>
                    <div style="text-align: center; margin: 30px 0;">
                      <a href="${url}" style="background: linear-gradient(135deg, #007bff, #0056b3); color: white; padding: 14px 28px; text-decoration: none; border-radius: 6px; display: inline-block; font-weight: 600;">🔒 Sign In Securely</a>
                    </div>
                    <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 6px; margin: 20px 0;">
                      <p style="color: #856404; margin: 0; font-size: 14px;">
                        <strong>⚠️ Security Notice:</strong> This link expires in 15 minutes. Never share this link with anyone.
                      </p>
                    </div>
                    <p style="color: #666; font-size: 12px;">
                      This email was sent to ${sanitizedEmail}. If you did not request this sign-in link, please ignore this email.
                    </p>
                  </div>
                `,
              });

              // Security: Log without sensitive data
              console.log('✅ Secure magic link sent:', {
                emailHash: crypto.createHash('sha256').update(email).digest('hex').substring(0, 8),
                messageId: result.messageId,
                timestamp: new Date().toISOString()
              });
            },
          }),
        ]
      : []),
  ],
  // Database adapter for storing user data in PostgreSQL
  // Using Prisma adapter with error handling
  ...(process.env.POSTGRE_DB && { adapter: PrismaAdapter(prisma) }),

  // Simplified callbacks for better compatibility
  callbacks: {
    async signIn({ user }) {
      // Basic validation
      return !!user?.email;
    },

    async jwt({ token, user }) {
      // Add user info to token
      if (user) {
        token.sub = user.id;
        token.email = user.email;
        token.name = user.name;
      }
      return token;
    },

    async session({ session, token }) {
      // Add user info to session
      if (session?.user && token) {
        session.user.id = token.sub;
        session.user.email = token.email;
        session.user.name = token.name;
      }
      return session;
    },

    async redirect({ url, baseUrl }) {
      // Allow same origin redirects
      if (url.startsWith("/")) return `${baseUrl}${url}`;
      if (new URL(url).origin === baseUrl) return url;
      return baseUrl;
    }
  },
  theme: {
    brandColor: config.colors.main,
    // Add you own logo below. Recommended size is rectangle (i.e. 200x50px) and show your logo + name.
    // It will be used in the login flow to display your logo. If you don't add it, it will look faded.
    logo: `https://${config.domainName}/logoAndName.png`,
  },
};
