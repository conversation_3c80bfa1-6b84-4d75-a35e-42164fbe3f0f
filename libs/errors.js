import { NextResponse } from 'next/server';
import { logSecurityEvent, SECURITY_EVENT_TYPES, SECURITY_SEVERITIES } from './security';

// Security: Ensure server-side only
if (typeof window !== 'undefined') {
  throw new Error('❌ SECURITY ERROR: Error handling library cannot be used on the client side!');
}

// Error codes for consistent error handling
export const ERROR_CODES = {
  // Authentication & Authorization
  AUTH_REQUIRED: 'AUTH_REQUIRED',
  INVALID_CREDENTIALS: 'INVALID_CREDENTIALS',
  ACCOUNT_LOCKED: 'ACCOUNT_LOCKED',
  ACCOUNT_DISABLED: 'ACCOUNT_DISABLED',
  INSUFFICIENT_PERMISSIONS: 'INSUFFICIENT_PERMISSIONS',
  TOKEN_EXPIRED: 'TOKEN_EXPIRED',
  TOKEN_INVALID: 'TOKEN_INVALID',

  // Validation
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  INVALID_INPUT: 'INVALID_INPUT',
  INVALID_EMAIL: 'INVALID_EMAIL',
  INVALID_JSON: 'INVALID_JSON',
  INVALID_CONTENT_TYPE: 'INVALID_CONTENT_TYPE',

  // Rate Limiting
  RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',
  IP_RATE_LIMIT_EXCEEDED: 'IP_RATE_LIMIT_EXCEEDED',
  EMAIL_RATE_LIMIT_EXCEEDED: 'EMAIL_RATE_LIMIT_EXCEEDED',

  // Resource Management
  RESOURCE_NOT_FOUND: 'RESOURCE_NOT_FOUND',
  RESOURCE_ALREADY_EXISTS: 'RESOURCE_ALREADY_EXISTS',
  RESOURCE_CONFLICT: 'RESOURCE_CONFLICT',

  // System Errors
  INTERNAL_ERROR: 'INTERNAL_ERROR',
  DATABASE_ERROR: 'DATABASE_ERROR',
  EXTERNAL_SERVICE_ERROR: 'EXTERNAL_SERVICE_ERROR',
  CONFIGURATION_ERROR: 'CONFIGURATION_ERROR',

  // Security
  CSRF_TOKEN_INVALID: 'CSRF_TOKEN_INVALID',
  WEBHOOK_VERIFICATION_FAILED: 'WEBHOOK_VERIFICATION_FAILED',
  SUSPICIOUS_ACTIVITY: 'SUSPICIOUS_ACTIVITY',
  UNAUTHORIZED_SOURCE: 'UNAUTHORIZED_SOURCE',

  // Request Issues
  BODY_TOO_LARGE: 'BODY_TOO_LARGE',
  MISSING_REQUIRED_FIELD: 'MISSING_REQUIRED_FIELD',
  DISPOSABLE_EMAIL: 'DISPOSABLE_EMAIL'
};

// HTTP status codes mapping
export const STATUS_CODES = {
  [ERROR_CODES.AUTH_REQUIRED]: 401,
  [ERROR_CODES.INVALID_CREDENTIALS]: 401,
  [ERROR_CODES.ACCOUNT_LOCKED]: 423,
  [ERROR_CODES.ACCOUNT_DISABLED]: 403,
  [ERROR_CODES.INSUFFICIENT_PERMISSIONS]: 403,
  [ERROR_CODES.TOKEN_EXPIRED]: 401,
  [ERROR_CODES.TOKEN_INVALID]: 401,

  [ERROR_CODES.VALIDATION_ERROR]: 400,
  [ERROR_CODES.INVALID_INPUT]: 400,
  [ERROR_CODES.INVALID_EMAIL]: 400,
  [ERROR_CODES.INVALID_JSON]: 400,
  [ERROR_CODES.INVALID_CONTENT_TYPE]: 400,

  [ERROR_CODES.RATE_LIMIT_EXCEEDED]: 429,
  [ERROR_CODES.IP_RATE_LIMIT_EXCEEDED]: 429,
  [ERROR_CODES.EMAIL_RATE_LIMIT_EXCEEDED]: 429,

  [ERROR_CODES.RESOURCE_NOT_FOUND]: 404,
  [ERROR_CODES.RESOURCE_ALREADY_EXISTS]: 409,
  [ERROR_CODES.RESOURCE_CONFLICT]: 409,

  [ERROR_CODES.INTERNAL_ERROR]: 500,
  [ERROR_CODES.DATABASE_ERROR]: 500,
  [ERROR_CODES.EXTERNAL_SERVICE_ERROR]: 502,
  [ERROR_CODES.CONFIGURATION_ERROR]: 500,

  [ERROR_CODES.CSRF_TOKEN_INVALID]: 403,
  [ERROR_CODES.WEBHOOK_VERIFICATION_FAILED]: 400,
  [ERROR_CODES.SUSPICIOUS_ACTIVITY]: 403,
  [ERROR_CODES.UNAUTHORIZED_SOURCE]: 403,

  [ERROR_CODES.BODY_TOO_LARGE]: 413,
  [ERROR_CODES.MISSING_REQUIRED_FIELD]: 400,
  [ERROR_CODES.DISPOSABLE_EMAIL]: 400
};

// Security-conscious error messages (don't expose internal details)
export const SAFE_ERROR_MESSAGES = {
  [ERROR_CODES.AUTH_REQUIRED]: 'Authentication required',
  [ERROR_CODES.INVALID_CREDENTIALS]: 'Invalid credentials',
  [ERROR_CODES.ACCOUNT_LOCKED]: 'Account temporarily locked',
  [ERROR_CODES.ACCOUNT_DISABLED]: 'Account disabled',
  [ERROR_CODES.INSUFFICIENT_PERMISSIONS]: 'Insufficient permissions',
  [ERROR_CODES.TOKEN_EXPIRED]: 'Session expired',
  [ERROR_CODES.TOKEN_INVALID]: 'Invalid session',

  [ERROR_CODES.VALIDATION_ERROR]: 'Validation failed',
  [ERROR_CODES.INVALID_INPUT]: 'Invalid input provided',
  [ERROR_CODES.INVALID_EMAIL]: 'Invalid email format',
  [ERROR_CODES.INVALID_JSON]: 'Invalid request format',
  [ERROR_CODES.INVALID_CONTENT_TYPE]: 'Invalid content type',

  [ERROR_CODES.RATE_LIMIT_EXCEEDED]: 'Too many requests',
  [ERROR_CODES.IP_RATE_LIMIT_EXCEEDED]: 'Too many requests from this location',
  [ERROR_CODES.EMAIL_RATE_LIMIT_EXCEEDED]: 'Email already submitted recently',

  [ERROR_CODES.RESOURCE_NOT_FOUND]: 'Resource not found',
  [ERROR_CODES.RESOURCE_ALREADY_EXISTS]: 'Resource already exists',
  [ERROR_CODES.RESOURCE_CONFLICT]: 'Resource conflict',

  [ERROR_CODES.INTERNAL_ERROR]: 'Internal server error',
  [ERROR_CODES.DATABASE_ERROR]: 'Service temporarily unavailable',
  [ERROR_CODES.EXTERNAL_SERVICE_ERROR]: 'External service unavailable',
  [ERROR_CODES.CONFIGURATION_ERROR]: 'Service configuration error',

  [ERROR_CODES.CSRF_TOKEN_INVALID]: 'Security validation failed',
  [ERROR_CODES.WEBHOOK_VERIFICATION_FAILED]: 'Webhook verification failed',
  [ERROR_CODES.SUSPICIOUS_ACTIVITY]: 'Suspicious activity detected',
  [ERROR_CODES.UNAUTHORIZED_SOURCE]: 'Unauthorized request source',

  [ERROR_CODES.BODY_TOO_LARGE]: 'Request too large',
  [ERROR_CODES.MISSING_REQUIRED_FIELD]: 'Required field missing',
  [ERROR_CODES.DISPOSABLE_EMAIL]: 'Disposable email addresses not allowed'
};

// Custom error class for application errors
export class AppError extends Error {
  constructor(code, message = null, details = null, statusCode = null) {
    super(message || SAFE_ERROR_MESSAGES[code] || 'Unknown error');
    this.name = 'AppError';
    this.code = code;
    this.statusCode = statusCode || STATUS_CODES[code] || 500;
    this.details = details;
    this.timestamp = new Date().toISOString();
  }

  toJSON() {
    return {
      error: this.message,
      code: this.code,
      ...(this.details && { details: this.details }),
      timestamp: this.timestamp
    };
  }
}

// Security: Create error response with logging
export async function createErrorResponse(error, request = null, userId = null) {
  const errorId = crypto.randomUUID();
  
  // Determine if this is an AppError or generic error
  const isAppError = error instanceof AppError;
  const errorCode = isAppError ? error.code : ERROR_CODES.INTERNAL_ERROR;
  const statusCode = isAppError ? error.statusCode : 500;
  const message = isAppError ? error.message : SAFE_ERROR_MESSAGES[ERROR_CODES.INTERNAL_ERROR];

  // Extract request metadata
  let ipAddress = null;
  let userAgent = null;
  
  if (request) {
    ipAddress = request.headers?.get?.('x-forwarded-for') || 
                request.headers?.get?.('x-real-ip') || 
                'unknown';
    userAgent = request.headers?.get?.('user-agent') || 'unknown';
  }

  // Log error details (server-side only)
  console.error(`[${errorId}] Error:`, {
    code: errorCode,
    message: isAppError ? error.message : error.message,
    stack: error.stack,
    userId,
    ipAddress,
    userAgent,
    timestamp: new Date().toISOString()
  });

  // Log security events for certain error types
  const securityErrorTypes = [
    ERROR_CODES.AUTH_REQUIRED,
    ERROR_CODES.INVALID_CREDENTIALS,
    ERROR_CODES.INSUFFICIENT_PERMISSIONS,
    ERROR_CODES.CSRF_TOKEN_INVALID,
    ERROR_CODES.WEBHOOK_VERIFICATION_FAILED,
    ERROR_CODES.SUSPICIOUS_ACTIVITY,
    ERROR_CODES.UNAUTHORIZED_SOURCE
  ];

  if (securityErrorTypes.includes(errorCode)) {
    await logSecurityEvent(
      SECURITY_EVENT_TYPES.UNAUTHORIZED_ACCESS,
      SECURITY_SEVERITIES.MEDIUM,
      `Security error: ${message}`,
      {
        errorCode,
        errorId,
        userId,
        originalError: isAppError ? error.details : null
      },
      ipAddress,
      userAgent,
      userId
    ).catch(logError => {
      console.error('Failed to log security event:', logError);
    });
  }

  // Create response object
  const responseBody = {
    error: message,
    code: errorCode,
    errorId,
    timestamp: new Date().toISOString()
  };

  // Add details for validation errors (safe to expose)
  if (errorCode === ERROR_CODES.VALIDATION_ERROR && isAppError && error.details) {
    responseBody.details = error.details;
  }

  // Add retry information for rate limiting
  if (errorCode.includes('RATE_LIMIT') && isAppError && error.details?.retryAfter) {
    responseBody.retryAfter = error.details.retryAfter;
  }

  return NextResponse.json(responseBody, { 
    status: statusCode,
    headers: {
      'X-Error-ID': errorId,
      'X-Content-Type-Options': 'nosniff',
      'Cache-Control': 'no-store'
    }
  });
}

// Validation error helper
export function createValidationError(errors) {
  return new AppError(
    ERROR_CODES.VALIDATION_ERROR,
    'Validation failed',
    Array.isArray(errors) ? errors : [errors]
  );
}

// Rate limit error helper
export function createRateLimitError(type = 'general', retryAfter = 60) {
  const errorCode = type === 'ip' ? ERROR_CODES.IP_RATE_LIMIT_EXCEEDED :
                   type === 'email' ? ERROR_CODES.EMAIL_RATE_LIMIT_EXCEEDED :
                   ERROR_CODES.RATE_LIMIT_EXCEEDED;
  
  return new AppError(
    errorCode,
    SAFE_ERROR_MESSAGES[errorCode],
    { retryAfter }
  );
}

// Authentication error helper
export function createAuthError(type = 'required') {
  const errorCode = type === 'invalid' ? ERROR_CODES.INVALID_CREDENTIALS :
                   type === 'expired' ? ERROR_CODES.TOKEN_EXPIRED :
                   type === 'locked' ? ERROR_CODES.ACCOUNT_LOCKED :
                   type === 'disabled' ? ERROR_CODES.ACCOUNT_DISABLED :
                   type === 'permissions' ? ERROR_CODES.INSUFFICIENT_PERMISSIONS :
                   ERROR_CODES.AUTH_REQUIRED;
  
  return new AppError(errorCode);
}

// Resource error helper
export function createResourceError(type = 'not_found') {
  const errorCode = type === 'exists' ? ERROR_CODES.RESOURCE_ALREADY_EXISTS :
                   type === 'conflict' ? ERROR_CODES.RESOURCE_CONFLICT :
                   ERROR_CODES.RESOURCE_NOT_FOUND;
  
  return new AppError(errorCode);
}

// Security error helper
export function createSecurityError(type = 'suspicious') {
  const errorCode = type === 'csrf' ? ERROR_CODES.CSRF_TOKEN_INVALID :
                   type === 'webhook' ? ERROR_CODES.WEBHOOK_VERIFICATION_FAILED :
                   type === 'source' ? ERROR_CODES.UNAUTHORIZED_SOURCE :
                   ERROR_CODES.SUSPICIOUS_ACTIVITY;
  
  return new AppError(errorCode);
}

export default {
  AppError,
  createErrorResponse,
  createValidationError,
  createRateLimitError,
  createAuthError,
  createResourceError,
  createSecurityError,
  ERROR_CODES,
  STATUS_CODES,
  SAFE_ERROR_MESSAGES
};
