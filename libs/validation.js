import validator from 'validator';
import { z } from 'zod';

// Security: Ensure server-side only
if (typeof window !== 'undefined') {
  throw new Error('❌ SECURITY ERROR: Validation library cannot be used on the client side!');
}

// Security: Common validation patterns
export const VALIDATION_PATTERNS = {
  // Alphanumeric with basic special characters
  SAFE_STRING: /^[a-zA-Z0-9\s\-_\.,']+$/,
  // Name validation (letters, spaces, hyphens, apostrophes)
  NAME: /^[a-zA-Z\s\-'\.]+$/,
  // Username (alphanumeric, underscore, hyphen)
  USERNAME: /^[a-zA-Z0-9_\-]+$/,
  // API key format
  API_KEY: /^sk_live_[a-f0-9]{64}$/,
  // UUID format
  UUID: /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i,
  // Phone number (international format)
  PHONE: /^\+?[1-9]\d{1,14}$/,
  // URL validation
  URL: /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/
};

// Security: Disposable email domains (common ones)
export const DISPOSABLE_EMAIL_DOMAINS = [
  '10minutemail.com', 'tempmail.org', 'guerrillamail.com', 'mailinator.com',
  'throwaway.email', 'temp-mail.org', 'getnada.com', 'maildrop.cc',
  'yopmail.com', 'sharklasers.com', 'grr.la', 'dispostable.com'
];

// Security: Comprehensive input sanitization
export function sanitizeInput(input, options = {}) {
  if (typeof input !== 'string') {
    return input;
  }

  let sanitized = input;

  // Trim whitespace
  if (options.trim !== false) {
    sanitized = sanitized.trim();
  }

  // Remove null bytes
  sanitized = sanitized.replace(/\0/g, '');

  // HTML escape if requested
  if (options.escapeHtml) {
    sanitized = validator.escape(sanitized);
  }

  // Remove control characters
  if (options.removeControlChars !== false) {
    sanitized = sanitized.replace(/[\x00-\x1F\x7F]/g, '');
  }

  // Normalize unicode
  if (options.normalizeUnicode !== false) {
    sanitized = sanitized.normalize('NFC');
  }

  // Length limit
  if (options.maxLength && sanitized.length > options.maxLength) {
    sanitized = sanitized.substring(0, options.maxLength);
  }

  return sanitized;
}

// Security: Enhanced email validation
export function validateEmail(email, options = {}) {
  if (!email || typeof email !== 'string') {
    return { valid: false, error: 'Email is required' };
  }

  const sanitized = sanitizeInput(email, { maxLength: 254 });

  // Basic format validation
  if (!validator.isEmail(sanitized)) {
    return { valid: false, error: 'Invalid email format' };
  }

  // Check for disposable email domains
  if (options.allowDisposable !== true) {
    const domain = sanitized.split('@')[1].toLowerCase();
    if (DISPOSABLE_EMAIL_DOMAINS.includes(domain)) {
      return { valid: false, error: 'Disposable email addresses not allowed' };
    }
  }

  // Additional domain validation
  if (options.allowedDomains && Array.isArray(options.allowedDomains)) {
    const domain = sanitized.split('@')[1].toLowerCase();
    if (!options.allowedDomains.includes(domain)) {
      return { valid: false, error: 'Email domain not allowed' };
    }
  }

  return { valid: true, value: sanitized.toLowerCase() };
}

// Security: Password strength validation
export function validatePassword(password, options = {}) {
  const minLength = options.minLength || 12;
  const requireUppercase = options.requireUppercase !== false;
  const requireLowercase = options.requireLowercase !== false;
  const requireNumbers = options.requireNumbers !== false;
  const requireSpecialChars = options.requireSpecialChars !== false;

  if (!password || typeof password !== 'string') {
    return { valid: false, error: 'Password is required' };
  }

  if (password.length < minLength) {
    return { valid: false, error: `Password must be at least ${minLength} characters long` };
  }

  if (requireUppercase && !/[A-Z]/.test(password)) {
    return { valid: false, error: 'Password must contain at least one uppercase letter' };
  }

  if (requireLowercase && !/[a-z]/.test(password)) {
    return { valid: false, error: 'Password must contain at least one lowercase letter' };
  }

  if (requireNumbers && !/\d/.test(password)) {
    return { valid: false, error: 'Password must contain at least one number' };
  }

  if (requireSpecialChars && !/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
    return { valid: false, error: 'Password must contain at least one special character' };
  }

  // Check for common weak passwords
  const commonPasswords = ['password', '123456', 'qwerty', 'admin', 'letmein'];
  if (commonPasswords.includes(password.toLowerCase())) {
    return { valid: false, error: 'Password is too common' };
  }

  return { valid: true };
}

// Security: Zod schemas for API validation
export const schemas = {
  // User registration/update
  user: z.object({
    name: z.string()
      .min(1, 'Name is required')
      .max(100, 'Name too long')
      .regex(VALIDATION_PATTERNS.NAME, 'Invalid name format'),
    email: z.string()
      .email('Invalid email format')
      .max(254, 'Email too long')
      .transform(val => val.toLowerCase().trim()),
    password: z.string()
      .min(12, 'Password must be at least 12 characters')
      .optional()
  }),

  // Lead capture
  lead: z.object({
    email: z.string()
      .email('Invalid email format')
      .max(254, 'Email too long')
      .transform(val => val.toLowerCase().trim()),
    name: z.string()
      .max(100, 'Name too long')
      .regex(VALIDATION_PATTERNS.NAME, 'Invalid name format')
      .optional(),
    source: z.string()
      .max(50, 'Source too long')
      .regex(VALIDATION_PATTERNS.USERNAME, 'Invalid source format')
      .optional()
  }),

  // API key operations
  apiKey: z.object({
    reason: z.string()
      .max(200, 'Reason too long')
      .regex(VALIDATION_PATTERNS.SAFE_STRING, 'Invalid reason format')
      .optional(),
    notifyEmail: z.boolean().optional(),
    requireConfirmation: z.boolean().optional(),
    confirmationToken: z.string()
      .regex(VALIDATION_PATTERNS.UUID, 'Invalid confirmation token')
      .optional()
  }),

  // Pagination
  pagination: z.object({
    page: z.number().int().min(1).max(1000).default(1),
    limit: z.number().int().min(1).max(100).default(50),
    sortBy: z.string().max(50).optional(),
    sortOrder: z.enum(['asc', 'desc']).default('desc'),
    search: z.string().max(100).optional()
  })
};

// Security: Request validation middleware
export function validateRequest(schema, data) {
  try {
    const result = schema.parse(data);
    return { success: true, data: result };
  } catch (error) {
    const errors = error.errors.map(err => ({
      field: err.path.join('.'),
      message: err.message
    }));
    return { success: false, errors };
  }
}

// Security: Rate limiting validation
export function validateRateLimit(rateLimit) {
  if (!rateLimit.allowed) {
    const retryAfter = Math.ceil((rateLimit.resetTime - Date.now()) / 1000);
    return {
      allowed: false,
      error: 'Rate limit exceeded',
      retryAfter: retryAfter,
      remaining: rateLimit.remaining
    };
  }
  return { allowed: true, remaining: rateLimit.remaining };
}

// Security: IP validation
export function validateIP(ip) {
  if (!ip || ip === 'unknown') {
    return { valid: false, error: 'IP address required' };
  }

  if (!validator.isIP(ip)) {
    return { valid: false, error: 'Invalid IP address format' };
  }

  // Check for private/local IPs in production
  if (process.env.NODE_ENV === 'production') {
    if (validator.isIP(ip, 4)) {
      // Check for private IPv4 ranges
      const parts = ip.split('.').map(Number);
      if (
        (parts[0] === 10) ||
        (parts[0] === 172 && parts[1] >= 16 && parts[1] <= 31) ||
        (parts[0] === 192 && parts[1] === 168) ||
        (parts[0] === 127)
      ) {
        return { valid: false, error: 'Private IP addresses not allowed in production' };
      }
    }
  }

  return { valid: true, value: ip };
}

// Security: Content type validation
export function validateContentType(contentType, allowedTypes = ['application/json']) {
  if (!contentType) {
    return { valid: false, error: 'Content-Type header required' };
  }

  const isAllowed = allowedTypes.some(type => contentType.includes(type));
  if (!isAllowed) {
    return { valid: false, error: `Content-Type must be one of: ${allowedTypes.join(', ')}` };
  }

  return { valid: true };
}

// Security: Request size validation
export function validateRequestSize(body, maxSize = 1024 * 1024) { // 1MB default
  if (!body) {
    return { valid: false, error: 'Request body required' };
  }

  const size = typeof body === 'string' ? body.length : JSON.stringify(body).length;
  if (size > maxSize) {
    return { valid: false, error: `Request body too large (max ${maxSize} bytes)` };
  }

  return { valid: true, size };
}

export default {
  sanitizeInput,
  validateEmail,
  validatePassword,
  validateRequest,
  validateRateLimit,
  validateIP,
  validateContentType,
  validateRequestSize,
  schemas,
  VALIDATION_PATTERNS,
  DISPOSABLE_EMAIL_DOMAINS
};
