import { prisma } from './prisma';
import { cache, CacheK<PERSON>s } from './redis';
import crypto from 'crypto';

// Security: Ensure server-side only
if (typeof window !== 'undefined') {
  throw new Error('❌ SECURITY ERROR: Security library cannot be used on the client side!');
}

// Security event types
export const SECURITY_EVENT_TYPES = {
  FAILED_LOGIN: 'FAILED_LOGIN',
  SUSPICIOUS_ACTIVITY: 'SUSPICIOUS_ACTIVITY',
  RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',
  INVALID_TOKEN: 'INVALID_TOKEN',
  UNAUTHORIZED_ACCESS: 'UNAUTHORIZED_ACCESS',
  API_KEY_MISUSE: 'API_KEY_MISUSE',
  BRUTE_FORCE_ATTEMPT: 'BRUTE_FORCE_ATTEMPT',
  ACCOUNT_LOCKED: 'ACCOUNT_LOCKED',
  WEBHOOK_VERIFICATION_FAILED: 'WEBHOOK_VERIFICATION_FAILED',
  CSRF_TOKEN_INVALID: 'CSRF_TOKEN_INVALID'
};

// Security event severities
export const SECURITY_SEVERITIES = {
  LOW: 'LOW',
  MEDIUM: 'MEDIUM',
  HIGH: 'HIGH',
  CRITICAL: 'CRITICAL'
};

// Audit log actions
export const AUDIT_ACTIONS = {
  USER_LOGIN: 'USER_LOGIN',
  USER_LOGOUT: 'USER_LOGOUT',
  USER_REGISTER: 'USER_REGISTER',
  API_KEY_GENERATED: 'API_KEY_GENERATED',
  API_KEY_REGENERATED: 'API_KEY_REGENERATED',
  API_KEY_REVOKED: 'API_KEY_REVOKED',
  API_KEY_COPIED: 'API_KEY_COPIED',
  API_KEY_VIEWED: 'API_KEY_VIEWED',
  PASSWORD_CHANGED: 'PASSWORD_CHANGED',
  EMAIL_CHANGED: 'EMAIL_CHANGED',
  PROFILE_UPDATED: 'PROFILE_UPDATED',
  ADMIN_ACCESS: 'ADMIN_ACCESS',
  DATA_EXPORT: 'DATA_EXPORT',
  WEBHOOK_PROCESSED: 'WEBHOOK_PROCESSED'
};

// Security: Log audit events
export async function logAuditEvent(userId, action, details = {}, ipAddress = null, userAgent = null) {
  try {
    await prisma.auditLog.create({
      data: {
        userId,
        action,
        details: {
          ...details,
          timestamp: new Date().toISOString()
        },
        ipAddress,
        userAgent,
        success: details.success !== false
      }
    });
  } catch (error) {
    console.error('Failed to log audit event:', error);
    // Don't throw - audit logging should not break the main flow
  }
}

// Security: Log security events
export async function logSecurityEvent(type, severity, description, metadata = {}, ipAddress = null, userAgent = null, userId = null) {
  try {
    const event = await prisma.securityEvent.create({
      data: {
        type,
        severity,
        description,
        ipAddress,
        userAgent,
        userId,
        metadata: {
          ...metadata,
          timestamp: new Date().toISOString()
        }
      }
    });

    // Cache critical events for immediate alerting
    if (severity === SECURITY_SEVERITIES.CRITICAL) {
      await cache.set(
        `security_alert:${event.id}`,
        event,
        3600 // 1 hour
      );
    }

    return event;
  } catch (error) {
    console.error('Failed to log security event:', error);
    // Don't throw - security logging should not break the main flow
  }
}

// Security: Check for suspicious activity patterns
export async function detectSuspiciousActivity(userId, ipAddress, action) {
  try {
    const now = new Date();
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);

    // Check for rapid successive actions
    const recentActions = await prisma.auditLog.count({
      where: {
        userId,
        action,
        createdAt: {
          gte: oneHourAgo
        }
      }
    });

    // Check for actions from multiple IPs
    const recentIPs = await prisma.auditLog.findMany({
      where: {
        userId,
        createdAt: {
          gte: oneHourAgo
        }
      },
      select: {
        ipAddress: true
      },
      distinct: ['ipAddress']
    });

    // Detect suspicious patterns
    const suspiciousPatterns = [];

    if (recentActions > 50) {
      suspiciousPatterns.push('High frequency actions');
    }

    if (recentIPs.length > 5) {
      suspiciousPatterns.push('Multiple IP addresses');
    }

    if (suspiciousPatterns.length > 0) {
      await logSecurityEvent(
        SECURITY_EVENT_TYPES.SUSPICIOUS_ACTIVITY,
        SECURITY_SEVERITIES.HIGH,
        `Suspicious activity detected: ${suspiciousPatterns.join(', ')}`,
        {
          userId,
          action,
          recentActions,
          recentIPs: recentIPs.length,
          patterns: suspiciousPatterns
        },
        ipAddress,
        null,
        userId
      );

      return { suspicious: true, patterns: suspiciousPatterns };
    }

    return { suspicious: false };
  } catch (error) {
    console.error('Failed to detect suspicious activity:', error);
    return { suspicious: false, error: error.message };
  }
}

// Security: Account lockout management
export async function checkAccountLockout(userId) {
  try {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        loginAttempts: true,
        lockedUntil: true,
        isActive: true
      }
    });

    if (!user) {
      return { locked: true, reason: 'User not found' };
    }

    if (!user.isActive) {
      return { locked: true, reason: 'Account deactivated' };
    }

    if (user.lockedUntil && user.lockedUntil > new Date()) {
      return { 
        locked: true, 
        reason: 'Account temporarily locked',
        lockedUntil: user.lockedUntil
      };
    }

    return { locked: false, loginAttempts: user.loginAttempts };
  } catch (error) {
    console.error('Failed to check account lockout:', error);
    return { locked: true, reason: 'Security check failed' };
  }
}

// Security: Handle failed login attempt
export async function handleFailedLogin(userId, ipAddress, userAgent) {
  try {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { loginAttempts: true, email: true }
    });

    if (!user) return;

    const newAttempts = (user.loginAttempts || 0) + 1;
    const maxAttempts = 5;
    const lockoutDuration = 30 * 60 * 1000; // 30 minutes

    let updateData = {
      loginAttempts: newAttempts
    };

    // Lock account after max attempts
    if (newAttempts >= maxAttempts) {
      updateData.lockedUntil = new Date(Date.now() + lockoutDuration);
      
      await logSecurityEvent(
        SECURITY_EVENT_TYPES.ACCOUNT_LOCKED,
        SECURITY_SEVERITIES.HIGH,
        `Account locked after ${maxAttempts} failed login attempts`,
        { userId, attempts: newAttempts },
        ipAddress,
        userAgent,
        userId
      );
    } else {
      await logSecurityEvent(
        SECURITY_EVENT_TYPES.FAILED_LOGIN,
        SECURITY_SEVERITIES.MEDIUM,
        `Failed login attempt ${newAttempts}/${maxAttempts}`,
        { userId, attempts: newAttempts },
        ipAddress,
        userAgent,
        userId
      );
    }

    await prisma.user.update({
      where: { id: userId },
      data: updateData
    });

    // Log audit event
    await logAuditEvent(
      userId,
      AUDIT_ACTIONS.USER_LOGIN,
      { success: false, attempts: newAttempts },
      ipAddress,
      userAgent
    );

  } catch (error) {
    console.error('Failed to handle failed login:', error);
  }
}

// Security: Handle successful login
export async function handleSuccessfulLogin(userId, ipAddress, userAgent) {
  try {
    await prisma.user.update({
      where: { id: userId },
      data: {
        loginAttempts: 0,
        lockedUntil: null,
        lastLoginAt: new Date()
      }
    });

    await logAuditEvent(
      userId,
      AUDIT_ACTIONS.USER_LOGIN,
      { success: true },
      ipAddress,
      userAgent
    );

    // Check for suspicious login patterns
    await detectSuspiciousActivity(userId, ipAddress, AUDIT_ACTIONS.USER_LOGIN);

  } catch (error) {
    console.error('Failed to handle successful login:', error);
  }
}

// Security: Generate secure tokens
export function generateSecureToken(length = 32) {
  return crypto.randomBytes(length).toString('hex');
}

// Security: Hash sensitive data
export function hashSensitiveData(data, salt = null) {
  const actualSalt = salt || crypto.randomBytes(16).toString('hex');
  const hash = crypto.pbkdf2Sync(data, actualSalt, 10000, 64, 'sha512').toString('hex');
  return { hash, salt: actualSalt };
}

// Security: Verify hashed data
export function verifySensitiveData(data, hash, salt) {
  const verifyHash = crypto.pbkdf2Sync(data, salt, 10000, 64, 'sha512').toString('hex');
  return hash === verifyHash;
}

// Security: Get security metrics
export async function getSecurityMetrics(timeframe = '24h') {
  try {
    const now = new Date();
    let startTime;

    switch (timeframe) {
      case '1h':
        startTime = new Date(now.getTime() - 60 * 60 * 1000);
        break;
      case '24h':
        startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        break;
      case '7d':
        startTime = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      default:
        startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    }

    const [securityEvents, auditLogs, failedLogins] = await Promise.all([
      prisma.securityEvent.count({
        where: {
          createdAt: { gte: startTime }
        }
      }),
      prisma.auditLog.count({
        where: {
          createdAt: { gte: startTime }
        }
      }),
      prisma.auditLog.count({
        where: {
          action: AUDIT_ACTIONS.USER_LOGIN,
          success: false,
          createdAt: { gte: startTime }
        }
      })
    ]);

    return {
      timeframe,
      securityEvents,
      auditLogs,
      failedLogins,
      generatedAt: new Date().toISOString()
    };

  } catch (error) {
    console.error('Failed to get security metrics:', error);
    return null;
  }
}

export default {
  logAuditEvent,
  logSecurityEvent,
  detectSuspiciousActivity,
  checkAccountLockout,
  handleFailedLogin,
  handleSuccessfulLogin,
  generateSecureToken,
  hashSensitiveData,
  verifySensitiveData,
  getSecurityMetrics,
  SECURITY_EVENT_TYPES,
  SECURITY_SEVERITIES,
  AUDIT_ACTIONS
};
