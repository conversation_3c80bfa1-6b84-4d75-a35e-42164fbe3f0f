module.exports = {
  apps: [
    {
      name: 'nextjs-frontend',
      script: 'node_modules/next/dist/bin/next',
      args: 'start',
      exec_mode: 'cluster',
      watch: false,
      env: {
        NODE_ENV: 'production',
        PORT: 3000
      },
      error_file: 'logs/error.log',
      out_file: 'logs/output.log',
      max_size: "100M",
      rotate_interval: "1d",
      max_logs: 7,
      log_directory_auto_create: true,
      merge_logs: true,
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      node_args: '',
      kill_timeout: 3000,
      wait_ready: false,
      listen_timeout: 10000,
      restart_delay: 4000,
      autorestart: true,
      "pre-start": "pnpm run db:deploy"
    },
  ]
};