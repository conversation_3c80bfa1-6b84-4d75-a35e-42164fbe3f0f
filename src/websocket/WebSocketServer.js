import { WebSocketServer } from 'ws';
import { v4 as uuidv4 } from 'uuid';
import { User } from '../models/User.js';
import { Admin } from '../models/Admin.js';
import { query } from '../config/database.js';
import { pubsub, cache } from '../config/redis.js';
import { getRealIP } from '../utils/ip.js';
import {
    checkWebSocketRateLimit,
    incrementWebSocketConnections,
    decrementWebSocketConnections
} from '../middleware/rateLimiter.js';
import { streamCreditManager, isStreamCreditsEnabled } from '../middleware/streamCredits.js';

export class WSServer {
    constructor(server) {
        this.wss = new WebSocketServer({
            server,
            path: '/ws'
        });
        
        this.clients = new Map(); // connectionId -> client info
        this.subscriptions = new Map(); // streamName -> Set of connectionIds
        this.userConnections = new Map(); // userId -> Set of connectionIds
        
        this.setupEventHandlers();
        this.setupRedisSubscriptions();
        this.startHeartbeat();
        
        console.log('✅ WebSocket server initialized');
    }

    // Create admin user adapter for WebSocket compatibility
    createAdminUserAdapter(admin) {
        return {
            id: `admin_${admin.id}`,
            email: admin.email,
            name: admin.name,
            api_key: admin.api_key,
            is_active: admin.is_active,
            tier_name: 'Admin',
            tier_id: 999, // Special admin tier ID
            max_credits_per_month: -1, // Unlimited
            credits_remaining: -1, // Unlimited
            credits_used_this_month: 0,
            max_requests_per_minute: -1, // Unlimited
            max_websocket_connections: -1, // Unlimited
            allowed_endpoints: ['*'], // Access to all endpoints
            allowed_streams: ['*'], // Access to all streams
            is_admin: true, // Flag to identify admin users
            admin_permissions: admin.permissions,
            created_at: admin.created_at,
            updated_at: admin.updated_at,
            last_login: admin.last_used
        };
    }

    // Authenticate WebSocket connection
    async authenticateConnection(req) {
        try {
            const url = new URL(req.url, 'http://localhost');
            const apiKey = url.searchParams.get('apiKey');

            if (!apiKey) {
                console.log('No API key provided');
                return null;
            }

            // First try to find regular user
            let user = await User.findByApiKey(apiKey);

            if (!user) {
                // If no regular user found, try admin user
                const admin = await Admin.findByApiKey(apiKey);
                if (admin) {
                    console.log('Admin user found for WebSocket connection:', admin.email);
                    user = this.createAdminUserAdapter(admin);
                } else {
                    console.log('No user or admin found for apiKey:', apiKey);
                    return null;
                }
            }

            if (!user.is_active) {
                console.log('User not active:', user.email);
                return null;
            }

            // Skip rate limiting for admin users
            if (!user.is_admin) {
                const rateLimitCheck = await checkWebSocketRateLimit(user.id, user);
                if (!rateLimitCheck.allowed) {
                    console.log('Rate limit exceeded for user:', user.id);
                    return null;
                }
            } else {
                console.log('Skipping rate limit check for admin user:', user.email);
            }

            // Check WebSocket connection limits DURING authentication (skip for admin users)
            if (!user.is_admin && user.max_websocket_connections !== -1) {
                const currentConnections = this.userConnections.get(user.id)?.size || 0;

                if (currentConnections >= user.max_websocket_connections) {
                    return {
                        error: 'connection_limit_exceeded',
                        limit: user.max_websocket_connections,
                        current: currentConnections
                    };
                }
            }

            return user;
        } catch (error) {
            console.error('WebSocket authentication error:', error);
            return null;
        }
    }

    // Setup WebSocket event handlers
    setupEventHandlers() {
        this.wss.on('connection', async (ws, req) => {
            const connectionId = uuidv4();
            const sessionId = uuidv4();

            // Authenticate user
            const authResult = await this.authenticateConnection(req);
            if (!authResult) {
                ws.close(1008, 'Authentication failed');
                return;
            }

            // Check if authentication returned an error (like connection limit exceeded)
            if (authResult.error) {
                if (authResult.error === 'connection_limit_exceeded') {
                    ws.close(1013, `Connection limit exceeded: ${authResult.limit} max connections`);
                } else {
                    ws.close(1008, 'Authentication failed');
                }
                return;
            }

            const user = authResult;

            // Store client information
            const clientInfo = {
                connectionId,
                sessionId,
                userId: user.id,
                user,
                ws,
                subscriptions: new Set(),
                lastActivity: Date.now(),
                ipAddress: getRealIP(req),
                userAgent: req.headers['user-agent']
            };
            
            this.clients.set(connectionId, clientInfo);
            
            // Track user connections
            if (!this.userConnections.has(user.id)) {
                this.userConnections.set(user.id, new Set());
            }
            this.userConnections.get(user.id).add(connectionId);
            
            // Increment connection count (skip for admin users)
            if (!user.is_admin) {
                await incrementWebSocketConnections(user.id);
            }
            
            // Store session in database
            await this.storeSession(clientInfo);
            
            console.log(`WebSocket connected: ${connectionId} (User: ${user.email})`);
            
            // Send welcome message
            this.sendMessage(ws, {
                type: 'connected',
                connectionId,
                sessionId,
                message: 'WebSocket connection established'
            });
            
            // Handle messages
            ws.on('message', (data) => this.handleMessage(connectionId, data));
            
            // Handle connection close
            ws.on('close', () => this.handleDisconnection(connectionId));
            
            // Handle errors
            ws.on('error', (error) => {
                console.error(`WebSocket error for ${connectionId}:`, error);
                this.handleDisconnection(connectionId);
            });
            
            // Update last activity
            ws.on('pong', () => {
                if (this.clients.has(connectionId)) {
                    this.clients.get(connectionId).lastActivity = Date.now();
                }
            });
        });
    }

    // Handle incoming messages
    async handleMessage(connectionId, data) {
        try {
            const client = this.clients.get(connectionId);
            if (!client) return;

            client.lastActivity = Date.now();

            let message;
            try {
                message = JSON.parse(data.toString());
                console.log(`📨 Received WebSocket message from ${connectionId}:`, JSON.stringify(message, null, 2));
            } catch (error) {
                console.error(`❌ Invalid JSON from ${connectionId}:`, data.toString());
                this.sendError(client.ws, 'Invalid JSON format');
                return;
            }

            const { type, payload } = message;
            
            switch (type) {
                case 'subscribe':
                    await this.handleSubscribe(client, payload);
                    break;
                    
                case 'unsubscribe':
                    await this.handleUnsubscribe(client, payload);
                    break;
                    
                case 'ping':
                    this.sendMessage(client.ws, { type: 'pong', timestamp: Date.now() });
                    break;
                    
                default:
                    this.sendError(client.ws, `Unknown message type: ${type}`);
            }
            
        } catch (error) {
            console.error('Error handling WebSocket message:', error);
            const client = this.clients.get(connectionId);
            if (client) {
                this.sendError(client.ws, 'Internal server error');
            }
        }
    }

    // Handle stream subscription
    async handleSubscribe(client, payload) {
        try {
            const { stream, parameters } = payload;

            if (!stream) {
                this.sendError(client.ws, 'Stream name is required');
                return;
            }

            // Check if user has access to this stream
            const hasAccess = await this.checkStreamAccess(client.user, stream);
            if (!hasAccess) {
                this.sendError(client.ws, `Access denied to stream: ${stream}`);
                return;
            }

            // Add to subscriptions
            if (!this.subscriptions.has(stream)) {
                this.subscriptions.set(stream, new Set());
            }

            this.subscriptions.get(stream).add(client.connectionId);
            client.subscriptions.add(stream);

            // Update database
            await this.updateSubscriptions(client);

            // Publish subscription event to Redis with parameters
            await pubsub.publish('stream_subscription', {
                type: 'subscribe',
                userId: client.userId,
                connectionId: client.connectionId,
                stream,
                parameters: parameters || {},
                timestamp: Date.now()
            });

            this.sendMessage(client.ws, {
                type: 'subscribed',
                stream,
                message: `Successfully subscribed to ${stream}`,
                parameters: parameters || {}
            });

            console.log(`User ${client.user.email} subscribed to ${stream}${parameters ? ` with parameters: ${JSON.stringify(parameters)}` : ''}`);

        } catch (error) {
            console.error('Error handling subscription:', error);
            this.sendError(client.ws, 'Subscription failed');
        }
    }

    // Handle stream unsubscription
    async handleUnsubscribe(client, payload) {
        try {
            const { stream } = payload;
            
            if (!stream) {
                this.sendError(client.ws, 'Stream name is required');
                return;
            }
            
            // Remove from subscriptions
            if (this.subscriptions.has(stream)) {
                this.subscriptions.get(stream).delete(client.connectionId);
                
                // Clean up empty stream subscriptions
                if (this.subscriptions.get(stream).size === 0) {
                    this.subscriptions.delete(stream);
                }
            }
            
            client.subscriptions.delete(stream);
            
            // Update database
            await this.updateSubscriptions(client);
            
            // Publish unsubscription event to Redis
            await pubsub.publish('stream_subscription', {
                type: 'unsubscribe',
                userId: client.userId,
                connectionId: client.connectionId,
                stream,
                timestamp: Date.now()
            });
            
            this.sendMessage(client.ws, {
                type: 'unsubscribed',
                stream,
                message: `Successfully unsubscribed from ${stream}`
            });
            
            console.log(`User ${client.user.email} unsubscribed from ${stream}`);
            
        } catch (error) {
            console.error('Error handling unsubscription:', error);
            this.sendError(client.ws, 'Unsubscription failed');
        }
    }

    // Handle client disconnection
    async handleDisconnection(connectionId) {
        try {
            const client = this.clients.get(connectionId);
            if (!client) return;
            
            console.log(`WebSocket disconnected: ${connectionId} (User: ${client.user.email})`);

            // Publish unsubscription events for all active subscriptions to trigger cleanup
            for (const stream of client.subscriptions) {
                try {
                    // Publish unsubscription event to Redis for SolanaTracker cleanup
                    await pubsub.publish('stream_subscription', {
                        type: 'unsubscribe',
                        userId: client.userId,
                        connectionId: connectionId,
                        stream,
                        timestamp: Date.now(),
                        reason: 'client_disconnect'
                    });

                    console.log(`🧹 Published unsubscription event for stream '${stream}' due to client disconnect (${connectionId})`);
                } catch (error) {
                    console.error(`❌ Failed to publish unsubscription event for stream '${stream}':`, error);
                }
            }

            // Remove from all subscriptions
            for (const stream of client.subscriptions) {
                if (this.subscriptions.has(stream)) {
                    this.subscriptions.get(stream).delete(connectionId);

                    // Clean up empty stream subscriptions
                    if (this.subscriptions.get(stream).size === 0) {
                        this.subscriptions.delete(stream);
                    }
                }
            }
            
            // Remove from user connections
            if (this.userConnections.has(client.userId)) {
                this.userConnections.get(client.userId).delete(connectionId);
                
                if (this.userConnections.get(client.userId).size === 0) {
                    this.userConnections.delete(client.userId);
                }
            }
            
            // Decrement connection count (skip for admin users)
            if (!client.user.is_admin) {
                await decrementWebSocketConnections(client.userId);
            }
            
            // Update database session
            await query(
                'UPDATE websocket_sessions SET disconnected_at = CURRENT_TIMESTAMP WHERE session_id = $1',
                [client.sessionId]
            );
            
            // Remove client
            this.clients.delete(connectionId);
            
        } catch (error) {
            console.error('Error handling disconnection:', error);
        }
    }

    // Check if user has access to stream
    async checkStreamAccess(user, streamName) {
        try {
            // Get stream requirements from database
            const streamResult = await query(`
                SELECT required_tier_id, is_active
                FROM stream_definitions
                WHERE stream_name = $1
            `, [streamName]);

            if (streamResult.rows.length === 0) {
                console.log(`❌ Stream '${streamName}' not found in database`);
                return false;
            }

            const stream = streamResult.rows[0];

            if (!stream.is_active) {
                console.log(`❌ Stream '${streamName}' is not active`);
                return false;
            }

            // Check if user has access to all streams (*) from their tier
            if (user.allowed_streams && user.allowed_streams.includes('*')) {
                console.log(`✅ User has wildcard access to stream '${streamName}'`);
                return true;
            }

            // Check if specific stream is allowed in user's tier
            if (user.allowed_streams && user.allowed_streams.includes(streamName)) {
                console.log(`✅ User has specific access to stream '${streamName}'`);
                return true;
            }

            // Check tier-based access (user tier >= required tier)
            if (user.tier_id >= stream.required_tier_id) {
                console.log(`✅ User tier ${user.tier_id} meets requirement for stream '${streamName}' (requires tier ${stream.required_tier_id})`);
                return true;
            }

            console.log(`❌ User denied access to stream '${streamName}': tier ${user.tier_id} < required ${stream.required_tier_id}, no wildcard or specific access`);
            return false;

        } catch (error) {
            console.error('Error checking stream access:', error);
            return false;
        }
    }

    // Store WebSocket session in database
    async storeSession(client) {
        try {
            await query(
                `INSERT INTO websocket_sessions (
                    session_id, user_id, connection_id, ip_address, user_agent
                ) VALUES ($1, $2, $3, $4, $5)`,
                [
                    client.sessionId,
                    client.userId,
                    client.connectionId,
                    client.ipAddress,
                    client.userAgent
                ]
            );
        } catch (error) {
            console.error('Error storing WebSocket session:', error);
        }
    }

    // Update subscriptions in database
    async updateSubscriptions(client) {
        try {
            const subscriptionsArray = Array.from(client.subscriptions);
            await query(
                'UPDATE websocket_sessions SET subscribed_streams = $1, last_activity = CURRENT_TIMESTAMP WHERE session_id = $2',
                [subscriptionsArray, client.sessionId]
            );
        } catch (error) {
            console.error('Error updating subscriptions:', error);
        }
    }

    // Setup Redis subscriptions for stream data
    setupRedisSubscriptions() {
        // Subscribe to all stream channels
        pubsub.subscribe('stream_data', (message) => {
            this.broadcastToStream(message.stream, message.data);
        });
        
        console.log('✅ Redis subscriptions setup for WebSocket');
    }

    // Broadcast message to all subscribers of a stream
    async broadcastToStream(streamName, data) {
        try {
            if (!this.subscriptions.has(streamName)) {
                return;
            }

            const subscribers = this.subscriptions.get(streamName);
            const message = {
                type: 'stream_data',
                stream: streamName,
                data,
                timestamp: Date.now()
            };

            // If stream credits are enabled, check and deduct credits
            if (isStreamCreditsEnabled()) {
                await this.broadcastWithCredits(streamName, message, subscribers);
            } else {
                // Original behavior - send to all subscribers without credit checks
                for (const connectionId of subscribers) {
                    const client = this.clients.get(connectionId);
                    if (client && client.ws.readyState === client.ws.OPEN) {
                        this.sendMessage(client.ws, message);
                    }
                }
            }

        } catch (error) {
            console.error('Error broadcasting to stream:', error);
        }
    }

    // Broadcast message with credit checking and deduction
    async broadcastWithCredits(streamName, message, subscribers) {
        try {
            // Get all user IDs for batch credit checking
            const userIds = [];
            const connectionToUser = new Map();

            for (const connectionId of subscribers) {
                const client = this.clients.get(connectionId);
                if (client && client.ws.readyState === client.ws.OPEN) {
                    userIds.push(client.userId);
                    connectionToUser.set(connectionId, client.userId);
                }
            }

            if (userIds.length === 0) {
                return;
            }

            // Batch check which users have sufficient credits
            const eligibleUsers = await streamCreditManager.batchCheckCredits(userIds, streamName);

            // Send messages and consume credits for eligible users
            const creditPromises = [];
            let sentCount = 0;
            let blockedCount = 0;

            for (const connectionId of subscribers) {
                const client = this.clients.get(connectionId);
                if (client && client.ws.readyState === client.ws.OPEN) {
                    const userId = client.userId;

                    if (eligibleUsers.has(userId)) {
                        // Send message
                        this.sendMessage(client.ws, message);
                        sentCount++;

                        // Queue credit consumption (don't await to avoid blocking)
                        creditPromises.push(
                            streamCreditManager.consumeStreamCredits(userId, streamName, connectionId)
                        );
                    } else {
                        // User doesn't have enough credits - send credit warning
                        // Get user-specific credit cost for the warning message
                        streamCreditManager.getUserStreamCreditCost(userId, streamName).then(userCreditCost => {
                            this.sendMessage(client.ws, {
                                type: 'credit_warning',
                                stream: streamName,
                                message: 'Insufficient credits to receive stream data',
                                required_credits: userCreditCost,
                                timestamp: Date.now()
                            });
                        }).catch(error => {
                            console.error('❌ Error getting user credit cost for warning:', error);
                            // Fallback to default cost
                            this.sendMessage(client.ws, {
                                type: 'credit_warning',
                                stream: streamName,
                                message: 'Insufficient credits to receive stream data',
                                required_credits: streamCreditManager.getStreamCreditCost(streamName),
                                timestamp: Date.now()
                            });
                        });
                        blockedCount++;
                    }
                }
            }

            // Process credit consumption in background
            if (creditPromises.length > 0) {
                Promise.all(creditPromises).catch(error => {
                    console.error('❌ Error processing stream credit consumption:', error);
                });
            }

            if (blockedCount > 0) {
                console.log(`💳 Stream ${streamName}: sent to ${sentCount} users, blocked ${blockedCount} users (insufficient credits)`);
            }

        } catch (error) {
            console.error('❌ Error in broadcastWithCredits:', error);
            // Fallback to sending without credit checks
            for (const connectionId of subscribers) {
                const client = this.clients.get(connectionId);
                if (client && client.ws.readyState === client.ws.OPEN) {
                    this.sendMessage(client.ws, message);
                }
            }
        }
    }

    // Send message to WebSocket client
    sendMessage(ws, message) {
        try {
            if (ws.readyState === ws.OPEN) {
                ws.send(JSON.stringify(message));
            }
        } catch (error) {
            console.error('Error sending WebSocket message:', error);
        }
    }

    // Send error message to WebSocket client
    sendError(ws, error) {
        this.sendMessage(ws, {
            type: 'error',
            error,
            timestamp: Date.now()
        });
    }

    // Start heartbeat to check connection health
    startHeartbeat() {
        const interval = parseInt(process.env.WS_HEARTBEAT_INTERVAL) || 30000;
        
        setInterval(() => {
            const now = Date.now();
            const timeout = interval * 2; // 2x heartbeat interval
            
            for (const [connectionId, client] of this.clients) {
                if (now - client.lastActivity > timeout) {
                    console.log(`Closing inactive WebSocket connection: ${connectionId}`);
                    client.ws.terminate();
                    this.handleDisconnection(connectionId);
                } else if (client.ws.readyState === client.ws.OPEN) {
                    client.ws.ping();
                }
            }
        }, interval);
    }

    // Get connection statistics
    getStats() {
        return {
            totalConnections: this.clients.size,
            totalStreams: this.subscriptions.size,
            totalUsers: this.userConnections.size,
            streams: Array.from(this.subscriptions.keys()).map(stream => ({
                name: stream,
                subscribers: this.subscriptions.get(stream).size
            }))
        };
    }
}
