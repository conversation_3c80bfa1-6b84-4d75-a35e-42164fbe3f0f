#!/usr/bin/env node

/**
 * Health Check Script
 * 
 * This script verifies that all systems are working correctly
 * after deployment. It checks database connectivity, schema,
 * and basic application functionality.
 */

import { prisma } from '../libs/prisma.js';
import { cache } from '../libs/redis.js';

async function healthCheck() {
  console.log('🏥 Starting health check...');
  console.log('⏰ Check started at:', new Date().toISOString());
  
  let allChecksPass = true;
  const results = {
    database: false,
    redis: false,
    schema: false,
    security: false
  };
  
  try {
    // Database connectivity check
    console.log('\n🗄️  Checking database connectivity...');
    try {
      await prisma.$queryRaw`SELECT 1`;
      console.log('✅ Database connection: OK');
      results.database = true;
    } catch (error) {
      console.log('❌ Database connection: FAILED');
      console.log('   Error:', error.message);
      allChecksPass = false;
    }
    
    // Redis connectivity check
    console.log('\n🔴 Checking Redis connectivity...');
    try {
      const redisHealth = await cache.ping();
      if (redisHealth) {
        console.log('✅ Redis connection: OK');
        results.redis = true;
      } else {
        console.log('⚠️  Redis connection: FAILED (will continue without caching)');
        results.redis = false;
      }
    } catch (error) {
      console.log('⚠️  Redis connection: FAILED (will continue without caching)');
      console.log('   Error:', error.message);
      results.redis = false;
    }
    
    // Database schema check
    console.log('\n📋 Checking database schema...');
    try {
      const tables = await prisma.$queryRaw`
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public'
        ORDER BY table_name
      `;
      
      const expectedTables = [
        'accounts',
        'audit_logs',
        'leads',
        'rate_limits',
        'security_events',
        'sessions',
        'users',
        'verificationtokens'
      ];
      
      const existingTables = tables.map(t => t.table_name);
      const missingTables = expectedTables.filter(table => !existingTables.includes(table));
      
      if (missingTables.length === 0) {
        console.log('✅ Database schema: OK');
        console.log(`   Found all ${expectedTables.length} expected tables`);
        results.schema = true;
      } else {
        console.log('❌ Database schema: INCOMPLETE');
        console.log('   Missing tables:', missingTables.join(', '));
        allChecksPass = false;
      }
      
      // List all tables
      console.log('   Tables found:');
      existingTables.forEach(table => {
        console.log(`     - ${table}`);
      });
      
    } catch (error) {
      console.log('❌ Database schema check: FAILED');
      console.log('   Error:', error.message);
      allChecksPass = false;
    }
    
    // Security features check
    console.log('\n🔒 Checking security features...');
    try {
      // Check if we can create an audit log entry
      const testAuditLog = await prisma.auditLog.create({
        data: {
          action: 'HEALTH_CHECK',
          details: {
            timestamp: new Date().toISOString(),
            checkType: 'automated_health_check'
          },
          success: true
        }
      });
      
      if (testAuditLog) {
        console.log('✅ Security features: OK');
        console.log('   Audit logging is functional');
        results.security = true;
      }
      
    } catch (error) {
      console.log('❌ Security features: FAILED');
      console.log('   Error:', error.message);
      allChecksPass = false;
    }
    
    // Overall health summary
    console.log('\n📊 HEALTH CHECK SUMMARY');
    console.log('═'.repeat(40));
    console.log(`🗄️  Database:     ${results.database ? '✅ OK' : '❌ FAILED'}`);
    console.log(`🔴 Redis:        ${results.redis ? '✅ OK' : '⚠️  DISABLED'}`);
    console.log(`📋 Schema:       ${results.schema ? '✅ OK' : '❌ FAILED'}`);
    console.log(`🔒 Security:     ${results.security ? '✅ OK' : '❌ FAILED'}`);
    console.log('═'.repeat(40));
    
    if (allChecksPass) {
      console.log('🎉 Overall Status: HEALTHY');
      console.log('✅ All critical systems are operational');
      
      // Log successful health check
      try {
        await prisma.auditLog.create({
          data: {
            action: 'HEALTH_CHECK_COMPLETED',
            details: {
              timestamp: new Date().toISOString(),
              results,
              status: 'healthy'
            },
            success: true
          }
        });
      } catch (error) {
        console.log('⚠️  Could not log health check result');
      }
      
    } else {
      console.log('⚠️  Overall Status: UNHEALTHY');
      console.log('❌ Some systems require attention');
      
      // Log failed health check
      try {
        await prisma.auditLog.create({
          data: {
            action: 'HEALTH_CHECK_FAILED',
            details: {
              timestamp: new Date().toISOString(),
              results,
              status: 'unhealthy'
            },
            success: false
          }
        });
      } catch (error) {
        console.log('⚠️  Could not log health check result');
      }
    }
    
    console.log('\n📚 Recommendations:');
    if (!results.database) {
      console.log('- Check database connection string and credentials');
      console.log('- Verify database server is running and accessible');
    }
    if (!results.redis) {
      console.log('- Check Redis server status (optional but recommended)');
      console.log('- Verify Redis connection settings');
    }
    if (!results.schema) {
      console.log('- Run database migration: pnpm run db:deploy');
      console.log('- Check for migration errors in logs');
    }
    if (!results.security) {
      console.log('- Verify audit_logs table exists and is writable');
      console.log('- Check database permissions');
    }
    
    if (allChecksPass) {
      process.exit(0);
    } else {
      process.exit(1);
    }
    
  } catch (error) {
    console.error('\n❌ Health check failed with error:', error.message);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
    await cache.disconnect();
  }
}

// Run the health check
healthCheck();
