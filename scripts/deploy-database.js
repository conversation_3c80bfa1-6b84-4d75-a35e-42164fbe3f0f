#!/usr/bin/env node

/**
 * Production Database Deployment Script
 * 
 * This script safely deploys database changes to production
 * It handles migrations, schema updates, and data integrity checks
 * 
 * SECURITY: This script should ONLY be run on the server during deployment
 */

import { execSync } from 'child_process';
import { prisma } from '../libs/prisma.js';
import { cache } from '../libs/redis.js';

async function deployDatabase() {
  console.log('🚀 Starting production database deployment...');
  
  try {
    // Check if environment variables are set
    if (!process.env.POSTGRE_DB) {
      throw new Error('❌ POSTGRE_DB environment variable is not set');
    }
    
    if (process.env.NODE_ENV !== 'production') {
      console.log('⚠️  Warning: Not running in production mode');
    }
    
    console.log('📋 Generating Prisma client...');
    execSync('npx prisma generate', { stdio: 'inherit' });
    
    console.log('🔍 Testing database connection...');
    await prisma.$queryRaw`SELECT 1`;
    console.log('✅ Database connection successful');
    
    console.log('📊 Checking current database schema...');
    const tables = await prisma.$queryRaw`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public'
    `;
    console.log(`📋 Found ${tables.length} existing tables`);
    
    // Check if this is a fresh database or needs migration
    const hasUsers = tables.some(table => table.table_name === 'users');
    const hasAuditLogs = tables.some(table => table.table_name === 'audit_logs');
    const hasSecurityEvents = tables.some(table => table.table_name === 'security_events');
    const hasRateLimits = tables.some(table => table.table_name === 'rate_limits');
    
    if (!hasUsers) {
      console.log('🆕 Fresh database detected - running initial setup...');
      execSync('npx prisma db push', { stdio: 'inherit' });
    } else {
      console.log('🔄 Existing database detected - checking for new tables...');
      
      // Check if we need to add new security tables
      const needsMigration = !hasAuditLogs || !hasSecurityEvents || !hasRateLimits;
      
      if (needsMigration) {
        console.log('📈 New security tables needed - applying schema updates...');
        
        // Create backup notification
        console.log('⚠️  IMPORTANT: Ensure you have a database backup before proceeding!');
        console.log('💾 Creating automatic backup log entry...');
        
        try {
          // Try to create audit log entry if table exists
          if (hasAuditLogs) {
            await prisma.auditLog.create({
              data: {
                action: 'DATABASE_MIGRATION_STARTED',
                details: {
                  timestamp: new Date().toISOString(),
                  tables_to_add: {
                    audit_logs: !hasAuditLogs,
                    security_events: !hasSecurityEvents,
                    rate_limits: !hasRateLimits
                  }
                },
                success: true
              }
            });
          }
        } catch (error) {
          console.log('ℹ️  Could not create audit log (table may not exist yet)');
        }
        
        // Apply schema changes
        execSync('npx prisma db push', { stdio: 'inherit' });
        
        console.log('✅ Schema migration completed successfully');
      } else {
        console.log('✅ Database schema is up to date');
      }
    }
    
    console.log('🔍 Testing Redis connection...');
    const redisHealth = await cache.ping();
    if (redisHealth) {
      console.log('✅ Redis connection successful');
    } else {
      console.log('⚠️  Redis connection failed - caching will be disabled');
    }
    
    // Verify all expected tables exist
    console.log('🔍 Verifying database schema...');
    const finalTables = await prisma.$queryRaw`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public'
      ORDER BY table_name
    `;
    
    const expectedTables = [
      'accounts',
      'audit_logs',
      'leads',
      'rate_limits',
      'security_events',
      'sessions',
      'users',
      'verificationtokens'
    ];
    
    const existingTableNames = finalTables.map(t => t.table_name);
    const missingTables = expectedTables.filter(table => !existingTableNames.includes(table));
    
    if (missingTables.length > 0) {
      console.log('⚠️  Warning: Missing expected tables:', missingTables);
    } else {
      console.log('✅ All expected tables are present');
    }
    
    console.log('📊 Final database schema:');
    existingTableNames.forEach(table => {
      console.log(`  - ${table}`);
    });
    
    // Create deployment success audit log
    try {
      await prisma.auditLog.create({
        data: {
          action: 'DATABASE_DEPLOYMENT_COMPLETED',
          details: {
            timestamp: new Date().toISOString(),
            tables_count: finalTables.length,
            tables: existingTableNames,
            deployment_type: hasUsers ? 'migration' : 'fresh_install'
          },
          success: true
        }
      });
      console.log('📝 Deployment logged to audit trail');
    } catch (error) {
      console.log('⚠️  Could not create deployment audit log:', error.message);
    }
    
    console.log('🎉 Database deployment completed successfully!');
    console.log('');
    console.log('✅ Your production database is ready with:');
    console.log('  - Enhanced user security fields');
    console.log('  - Comprehensive audit logging');
    console.log('  - Security event tracking');
    console.log('  - Rate limiting support');
    console.log('  - All NextAuth.js tables');
    
  } catch (error) {
    console.error('❌ Database deployment failed:', error.message);
    
    // Try to log the failure
    try {
      await prisma.auditLog.create({
        data: {
          action: 'DATABASE_DEPLOYMENT_FAILED',
          details: {
            timestamp: new Date().toISOString(),
            error: error.message,
            stack: error.stack
          },
          success: false
        }
      });
    } catch (logError) {
      console.error('❌ Could not log deployment failure:', logError.message);
    }
    
    process.exit(1);
  } finally {
    await prisma.$disconnect();
    await cache.disconnect();
  }
}

// Run the deployment
deployDatabase();
