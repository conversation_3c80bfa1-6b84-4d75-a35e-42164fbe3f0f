/**
 * Comprehensive Security Test Suite
 * Tests all security implementations and vulnerabilities
 */

import { describe, it, expect, beforeAll, afterAll } from '@jest/globals';
import { NextRequest } from 'next/server';

// Mock environment variables for testing
process.env.NEXTAUTH_SECRET = 'test-secret-key-for-testing-only';
process.env.STRIPE_SECRET_KEY = 'sk_test_123456789';
process.env.STRIPE_WEBHOOK_SECRET = 'whsec_test_123456789';

describe('Security Test Suite', () => {
  
  describe('Authentication & Authorization', () => {
    
    it('should reject requests without authentication', async () => {
      const { POST } = await import('../app/api/user/api-key/route.js');
      const request = new NextRequest('http://localhost:3000/api/user/api-key', {
        method: 'POST',
        headers: { 'content-type': 'application/json' }
      });
      
      const response = await POST(request);
      expect(response.status).toBe(401);
      
      const data = await response.json();
      expect(data.code).toBe('AUTH_REQUIRED');
    });

    it('should reject admin endpoints for non-admin users', async () => {
      const { GET } = await import('../app/api/users/route.js');
      const request = new NextRequest('http://localhost:3000/api/users', {
        method: 'GET',
        headers: { 'content-type': 'application/json' }
      });
      
      const response = await GET(request);
      expect(response.status).toBe(401);
    });

    it('should validate JWT tokens properly', async () => {
      // Test with invalid JWT token
      const request = new NextRequest('http://localhost:3000/api/user/api-key', {
        method: 'GET',
        headers: { 
          'content-type': 'application/json',
          'authorization': 'Bearer invalid-token'
        }
      });
      
      // This would be handled by NextAuth middleware
      expect(request.headers.get('authorization')).toBe('Bearer invalid-token');
    });
  });

  describe('Input Validation', () => {
    
    it('should validate email format', async () => {
      const { validateEmail } = await import('../libs/validation.js');
      
      // Valid emails
      expect(validateEmail('<EMAIL>').valid).toBe(true);
      expect(validateEmail('<EMAIL>').valid).toBe(true);
      
      // Invalid emails
      expect(validateEmail('invalid-email').valid).toBe(false);
      expect(validateEmail('test@').valid).toBe(false);
      expect(validateEmail('@domain.com').valid).toBe(false);
      expect(validateEmail('<EMAIL>').valid).toBe(false);
    });

    it('should reject disposable email domains', async () => {
      const { validateEmail } = await import('../libs/validation.js');
      
      const disposableEmails = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
      ];
      
      disposableEmails.forEach(email => {
        const result = validateEmail(email);
        expect(result.valid).toBe(false);
        expect(result.error).toContain('Disposable email');
      });
    });

    it('should sanitize input properly', async () => {
      const { sanitizeInput } = await import('../libs/validation.js');
      
      // Test XSS prevention
      const maliciousInput = '<script>alert("xss")</script>';
      const sanitized = sanitizeInput(maliciousInput, { escapeHtml: true });
      expect(sanitized).not.toContain('<script>');
      
      // Test null byte removal
      const nullByteInput = 'test\0malicious';
      const cleaned = sanitizeInput(nullByteInput);
      expect(cleaned).toBe('testmalicious');
      
      // Test length limiting
      const longInput = 'a'.repeat(1000);
      const limited = sanitizeInput(longInput, { maxLength: 100 });
      expect(limited.length).toBe(100);
    });

    it('should validate password strength', async () => {
      const { validatePassword } = await import('../libs/validation.js');
      
      // Strong password
      const strongPassword = 'MyStr0ng!P@ssw0rd123';
      expect(validatePassword(strongPassword).valid).toBe(true);
      
      // Weak passwords
      expect(validatePassword('password').valid).toBe(false);
      expect(validatePassword('123456').valid).toBe(false);
      expect(validatePassword('short').valid).toBe(false);
      expect(validatePassword('nouppercase123!').valid).toBe(false);
      expect(validatePassword('NOLOWERCASE123!').valid).toBe(false);
      expect(validatePassword('NoNumbers!').valid).toBe(false);
      expect(validatePassword('NoSpecialChars123').valid).toBe(false);
    });
  });

  describe('Rate Limiting', () => {
    
    it('should enforce rate limits', async () => {
      const { checkRateLimit } = await import('../libs/redis.js');
      
      const testKey = 'test-rate-limit-key';
      const limit = 3;
      const windowMs = 60000; // 1 minute
      
      // First few requests should be allowed
      for (let i = 0; i < limit; i++) {
        const result = await checkRateLimit(testKey, limit, windowMs);
        expect(result.allowed).toBe(true);
      }
      
      // Next request should be blocked
      const blockedResult = await checkRateLimit(testKey, limit, windowMs);
      expect(blockedResult.allowed).toBe(false);
    });

    it('should handle different rate limit types', async () => {
      const { POST } = await import('../app/api/lead/route.js');
      
      // Test IP-based rate limiting
      const requests = [];
      for (let i = 0; i < 5; i++) {
        const request = new NextRequest('http://localhost:3000/api/lead', {
          method: 'POST',
          headers: { 
            'content-type': 'application/json',
            'x-forwarded-for': '*************'
          },
          body: JSON.stringify({ email: `test${i}@example.com` })
        });
        requests.push(POST(request));
      }
      
      const responses = await Promise.all(requests);
      const rateLimitedResponses = responses.filter(r => r.status === 429);
      expect(rateLimitedResponses.length).toBeGreaterThan(0);
    });
  });

  describe('CSRF Protection', () => {
    
    it('should require CSRF tokens for state-changing operations', async () => {
      const { POST } = await import('../app/api/user/api-key/regenerate/route.js');
      
      const request = new NextRequest('http://localhost:3000/api/user/api-key/regenerate', {
        method: 'POST',
        headers: { 
          'content-type': 'application/json'
          // Missing X-CSRF-Token header
        },
        body: JSON.stringify({})
      });
      
      // This would be caught by middleware in a real scenario
      expect(request.headers.get('x-csrf-token')).toBeNull();
    });
  });

  describe('SQL Injection Prevention', () => {
    
    it('should use parameterized queries', async () => {
      // Test that our Prisma queries are safe
      const maliciousInput = "'; DROP TABLE users; --";
      
      // This should be handled safely by Prisma
      const { validateRequest, schemas } = await import('../libs/validation.js');
      const result = validateRequest(schemas.lead, { 
        email: `test${maliciousInput}@example.com` 
      });
      
      expect(result.success).toBe(false);
    });
  });

  describe('XSS Prevention', () => {
    
    it('should escape HTML in user inputs', async () => {
      const { sanitizeInput } = await import('../libs/validation.js');
      
      const xssPayloads = [
        '<script>alert("xss")</script>',
        '<img src="x" onerror="alert(1)">',
        'javascript:alert("xss")',
        '<svg onload="alert(1)">',
        '"><script>alert("xss")</script>'
      ];
      
      xssPayloads.forEach(payload => {
        const sanitized = sanitizeInput(payload, { escapeHtml: true });
        expect(sanitized).not.toContain('<script>');
        expect(sanitized).not.toContain('javascript:');
        expect(sanitized).not.toContain('onerror=');
        expect(sanitized).not.toContain('onload=');
      });
    });
  });

  describe('API Key Security', () => {
    
    it('should generate cryptographically secure API keys', async () => {
      const crypto = await import('crypto');
      
      // Test API key generation
      const generateSecureApiKey = () => {
        const prefix = "sk_live_";
        const randomBytes = crypto.randomBytes(32);
        const key = randomBytes.toString('hex');
        return `${prefix}${key}`;
      };
      
      const apiKey1 = generateSecureApiKey();
      const apiKey2 = generateSecureApiKey();
      
      // Keys should be different
      expect(apiKey1).not.toBe(apiKey2);
      
      // Keys should have correct format
      expect(apiKey1).toMatch(/^sk_live_[a-f0-9]{64}$/);
      expect(apiKey2).toMatch(/^sk_live_[a-f0-9]{64}$/);
      
      // Keys should be long enough
      expect(apiKey1.length).toBe(72); // sk_live_ (8) + 64 hex chars
    });

    it('should hash API keys for storage', async () => {
      const crypto = await import('crypto');
      
      const apiKey = 'sk_live_1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef';
      const hash1 = crypto.createHash('sha256').update(apiKey).digest('hex');
      const hash2 = crypto.createHash('sha256').update(apiKey).digest('hex');
      
      // Same input should produce same hash
      expect(hash1).toBe(hash2);
      
      // Hash should be different from original
      expect(hash1).not.toBe(apiKey);
      
      // Hash should be 64 characters (SHA-256)
      expect(hash1.length).toBe(64);
    });
  });

  describe('Webhook Security', () => {
    
    it('should verify webhook signatures', async () => {
      const crypto = await import('crypto');
      
      const payload = JSON.stringify({ test: 'data' });
      const secret = 'test-webhook-secret';
      
      // Create valid signature
      const signature = crypto
        .createHmac('sha256', secret)
        .update(payload)
        .digest('hex');
      
      const validSignature = `sha256=${signature}`;
      
      // Verify signature
      const expectedSignature = crypto
        .createHmac('sha256', secret)
        .update(payload)
        .digest('hex');
      
      expect(signature).toBe(expectedSignature);
      
      // Test invalid signature
      const invalidSignature = 'sha256=invalid';
      expect(invalidSignature.split('=')[1]).not.toBe(expectedSignature);
    });
  });

  describe('Error Handling', () => {
    
    it('should not expose sensitive information in errors', async () => {
      const { createErrorResponse, AppError, ERROR_CODES } = await import('../libs/errors.js');
      
      const sensitiveError = new Error('Database connection failed: password=secret123');
      const request = new NextRequest('http://localhost:3000/test');
      
      const response = await createErrorResponse(sensitiveError, request);
      const data = await response.json();
      
      // Should not expose sensitive details
      expect(data.error).not.toContain('password=secret123');
      expect(data.error).toBe('Internal server error');
      expect(data.code).toBe(ERROR_CODES.INTERNAL_ERROR);
    });

    it('should provide safe error messages', async () => {
      const { AppError, ERROR_CODES, SAFE_ERROR_MESSAGES } = await import('../libs/errors.js');
      
      const authError = new AppError(ERROR_CODES.AUTH_REQUIRED);
      expect(authError.message).toBe(SAFE_ERROR_MESSAGES[ERROR_CODES.AUTH_REQUIRED]);
      expect(authError.message).toBe('Authentication required');
      
      const validationError = new AppError(ERROR_CODES.VALIDATION_ERROR);
      expect(validationError.message).toBe('Validation failed');
    });
  });

  describe('Security Headers', () => {
    
    it('should set appropriate security headers', async () => {
      // Test that middleware sets security headers
      const { middleware } = await import('../middleware.js');
      
      const request = new NextRequest('http://localhost:3000/dashboard');
      const response = await middleware(request);
      
      // Check for security headers
      expect(response.headers.get('x-frame-options')).toBe('DENY');
      expect(response.headers.get('x-content-type-options')).toBe('nosniff');
      expect(response.headers.get('referrer-policy')).toBe('strict-origin-when-cross-origin');
      expect(response.headers.has('content-security-policy')).toBe(true);
    });
  });

  describe('Session Security', () => {
    
    it('should use secure session configuration', async () => {
      const { authOptions } = await import('../libs/next-auth.js');
      
      // Check session strategy
      expect(authOptions.session.strategy).toBe('jwt');
      expect(authOptions.session.maxAge).toBe(24 * 60 * 60); // 24 hours
      
      // Check cookie security
      expect(authOptions.cookies.sessionToken.options.httpOnly).toBe(true);
      expect(authOptions.cookies.sessionToken.options.sameSite).toBe('lax');
      
      // In production, should be secure
      if (process.env.NODE_ENV === 'production') {
        expect(authOptions.cookies.sessionToken.options.secure).toBe(true);
      }
    });
  });
});

// Helper function to run security tests
export async function runSecurityTests() {
  console.log('🔒 Running comprehensive security tests...');
  
  const testResults = {
    passed: 0,
    failed: 0,
    total: 0,
    details: []
  };
  
  try {
    // This would integrate with your test runner
    console.log('✅ All security tests completed');
    return testResults;
  } catch (error) {
    console.error('❌ Security tests failed:', error);
    throw error;
  }
}
