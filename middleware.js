import { NextResponse } from 'next/server';
import { getToken } from 'next-auth/jwt';

// Security: Define protected routes that require authentication
const PROTECTED_ROUTES = [
  '/dashboard',
  '/api/user',
  '/api/stripe/create-checkout',
  '/api/stripe/create-portal'
];

// Security: Define admin routes that require admin privileges
const ADMIN_ROUTES = [
  '/api/users',
  '/admin'
];

// Security: Define public API routes that don't need CSRF protection
const PUBLIC_API_ROUTES = [
  '/api/auth',
  '/api/webhook'
];

// Security: Generate nonce for CSP
function generateNonce() {
  return Buffer.from(crypto.getRandomValues(new Uint8Array(16))).toString('base64');
}

// Security: Add comprehensive security headers
function addSecurityHeaders(response, nonce) {
  // Only apply strict CSP in production
  if (process.env.NODE_ENV === 'production') {
    // Content Security Policy - Production settings
    const csp = [
      "default-src 'self'",
      `script-src 'self' 'unsafe-eval' 'unsafe-inline' 'nonce-${nonce}' https://js.stripe.com https://checkout.stripe.com https://client.crisp.chat`,
      "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://client.crisp.chat",
      "font-src 'self' https://fonts.gstatic.com https://client.crisp.chat",
      "img-src 'self' data: https: blob:",
      "connect-src 'self' https://api.stripe.com https://checkout.stripe.com https://client.crisp.chat wss: ws:",
      "frame-src 'self' https://js.stripe.com https://checkout.stripe.com https://client.crisp.chat",
      "object-src 'none'",
      "base-uri 'self'",
      "form-action 'self'",
      "frame-ancestors 'none'",
      "upgrade-insecure-requests"
    ].join('; ');

    response.headers.set('Content-Security-Policy', csp);
    response.headers.set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');
  } else {
    // Development - More permissive CSP
    const devCsp = [
      "default-src 'self'",
      "script-src 'self' 'unsafe-eval' 'unsafe-inline' https: http:",
      "style-src 'self' 'unsafe-inline' https: http:",
      "font-src 'self' https: http: data:",
      "img-src 'self' data: https: http: blob:",
      "connect-src 'self' https: http: ws: wss:",
      "frame-src 'self' https: http:",
      "object-src 'none'"
    ].join('; ');

    response.headers.set('Content-Security-Policy', devCsp);
  }

  // Set other security headers
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
  response.headers.set('X-XSS-Protection', '1; mode=block');
  response.headers.set('Permissions-Policy', 'camera=(), microphone=(), geolocation=(), payment=()');

  // Remove server information
  response.headers.set('Server', '');
  response.headers.delete('X-Powered-By');

  return response;
}

// Security: Validate CSRF token for state-changing operations
function validateCSRFToken(request) {
  const method = request.method;

  // Only check CSRF for state-changing methods
  if (!['POST', 'PUT', 'DELETE', 'PATCH'].includes(method)) {
    return true;
  }

  // Skip CSRF for public API routes and NextAuth routes
  const pathname = request.nextUrl.pathname;
  if (PUBLIC_API_ROUTES.some(route => pathname.startsWith(route))) {
    return true;
  }

  // Skip CSRF for NextAuth internal routes
  if (pathname.startsWith('/api/auth/')) {
    return true;
  }

  // For development, be more lenient with CSRF validation
  if (process.env.NODE_ENV === 'development') {
    return true;
  }

  // Check for CSRF token in header
  const csrfToken = request.headers.get('X-CSRF-Token');
  const sessionToken = request.headers.get('Authorization') || request.cookies.get('next-auth.session-token')?.value;

  if (!csrfToken || !sessionToken) {
    return false;
  }

  // Simple CSRF validation (in production, use more sophisticated validation)
  // This is a basic implementation - consider using a proper CSRF library
  return csrfToken.length > 10; // Basic validation
}

// Security: Rate limiting check (basic implementation)
const rateLimitMap = new Map();

function checkRateLimit(ip, limit = 100, windowMs = 60000) {
  const now = Date.now();
  const windowStart = now - windowMs;

  if (!rateLimitMap.has(ip)) {
    rateLimitMap.set(ip, []);
  }

  const requests = rateLimitMap.get(ip);
  
  // Remove old requests outside the window
  const validRequests = requests.filter(timestamp => timestamp > windowStart);
  rateLimitMap.set(ip, validRequests);

  // Check if limit exceeded
  if (validRequests.length >= limit) {
    return false;
  }

  // Add current request
  validRequests.push(now);
  return true;
}

export async function middleware(request) {
  const { pathname } = request.nextUrl;
  const nonce = generateNonce();

  // Skip middleware for static files and Next.js internals
  if (
    pathname.startsWith('/_next/') ||
    pathname.startsWith('/api/auth/') ||
    pathname.includes('.') ||
    pathname === '/favicon.ico'
  ) {
    return NextResponse.next();
  }

  // Get client IP for rate limiting
  const clientIP = request.headers.get('x-forwarded-for') ||
                   request.headers.get('x-real-ip') ||
                   request.ip ||
                   'unknown';

  // Basic rate limiting (100 requests per minute per IP) - disabled in development
  if (process.env.NODE_ENV === 'production' && !checkRateLimit(clientIP, 100, 60000)) {
    return new NextResponse('Too Many Requests', {
      status: 429,
      headers: {
        'Retry-After': '60',
        'X-RateLimit-Limit': '100',
        'X-RateLimit-Remaining': '0',
        'X-RateLimit-Reset': String(Math.ceil(Date.now() / 1000) + 60)
      }
    });
  }

  // CSRF Protection for API routes - disabled in development
  if (process.env.NODE_ENV === 'production' && pathname.startsWith('/api/') && !validateCSRFToken(request)) {
    return new NextResponse('CSRF Token Invalid', {
      status: 403,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }

  // Authentication check for protected routes
  if (PROTECTED_ROUTES.some(route => pathname.startsWith(route))) {
    const token = await getToken({ 
      req: request, 
      secret: process.env.NEXTAUTH_SECRET 
    });

    if (!token) {
      // Redirect to login for page routes
      if (!pathname.startsWith('/api/')) {
        const loginUrl = new URL('/api/auth/signin', request.url);
        loginUrl.searchParams.set('callbackUrl', request.url);
        return NextResponse.redirect(loginUrl);
      }
      
      // Return 401 for API routes
      return new NextResponse('Unauthorized', { 
        status: 401,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }
  }

  // Admin route protection
  if (ADMIN_ROUTES.some(route => pathname.startsWith(route))) {
    const token = await getToken({ 
      req: request, 
      secret: process.env.NEXTAUTH_SECRET 
    });

    if (!token) {
      return new NextResponse('Unauthorized', { status: 401 });
    }

    // Check admin privileges (basic implementation)
    const adminEmails = process.env.ADMIN_EMAILS?.split(',') || [];
    if (!adminEmails.includes(token.email)) {
      return new NextResponse('Forbidden', { status: 403 });
    }
  }

  // Create response
  const response = NextResponse.next();

  // Add security headers to all responses
  addSecurityHeaders(response, nonce);

  // Add CSRF token to response headers for client-side use
  if (!pathname.startsWith('/api/')) {
    response.headers.set('X-CSRF-Token', generateNonce());
  }

  // Add rate limit headers
  response.headers.set('X-RateLimit-Limit', '100');
  response.headers.set('X-RateLimit-Window', '60');

  return response;
}

// Configure which routes the middleware runs on
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|public/).*)',
  ],
};
