"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import apiClient from "@/libs/api";
import { toast } from "react-hot-toast";

const ApiKeyManager = () => {
  const { data: session } = useSession();
  const [isLoading, setIsLoading] = useState(true);
  const [isGenerating, setIsGenerating] = useState(false);
  const [apiKey, setApiKey] = useState("");
  const [maskedKey, setMaskedKey] = useState("");
  const [showKey, setShowKey] = useState(false);
  const [lastUsed, setLastUsed] = useState(null);
  const [usageCount, setUsageCount] = useState(0);
  const [hasAccess, setHasAccess] = useState(false);
  const [tier, setTier] = useState('free');

  // Mask API key for security
  const maskApiKey = (key) => {
    if (!key) return "";
    return `${key.substring(0, 12)}${"•".repeat(32)}${key.substring(key.length - 4)}`;
  };

  // Fetch API key information from API database via frontend API
  useEffect(() => {
    const fetchApiKey = async () => {
      if (!session?.user?.id) return;

      try {
        setIsLoading(true);
        const response = await apiClient.get('/user/api-key');

        if (response.success) {
          setHasAccess(response.hasAccess !== false);

          if (response.hasAccess !== false) {
            if (response.apiKey) {
              // New API key or regenerated key - we have the actual key
              setApiKey(response.apiKey);
              setMaskedKey(maskApiKey(response.apiKey));
            } else if (response.hasApiKey) {
              // Existing API key - we only have metadata
              setApiKey(""); // Clear any previous key
              setMaskedKey(response.keyPreview || "sk_live_••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••");
            }
            setLastUsed(response.lastUsed);
            setUsageCount(response.usageCount || 0);
            setTier(response.tier || 'free');
          }
        }
      } catch (error) {
        console.error('Failed to fetch API key:', error);
        if (error.message?.includes('subscription')) {
          setHasAccess(false);
        } else {
          toast.error('Failed to load API key information');
        }
      } finally {
        setIsLoading(false);
      }
    };

    fetchApiKey();
  }, [session]);

  const handleCopyKey = async () => {
    if (!apiKey) {
      toast.error('API key not available. Please regenerate to get a new key.');
      return;
    }

    try {
      await navigator.clipboard.writeText(apiKey);
      toast.success('API key copied to clipboard');
    } catch (error) {
      console.error('Failed to copy API key:', error);
      toast.error('Failed to copy API key');
    }
  };

  const handleRegenerateKey = async () => {
    if (!hasAccess) {
      toast.error('API key generation requires an active subscription');
      return;
    }

    if (!confirm('Are you sure you want to regenerate your API key? This will invalidate the current key and may break existing integrations.')) {
      return;
    }

    try {
      setIsGenerating(true);
      const response = await apiClient.post('/user/api-key');

      if (response.success) {
        setApiKey(response.apiKey);
        setMaskedKey(maskApiKey(response.apiKey));
        setLastUsed(response.lastUsed);
        setUsageCount(response.usageCount || 0);
        setShowKey(true); // Show the new key immediately
        toast.success('New API key generated successfully!');
      } else {
        toast.error(response.error || 'Failed to generate API key');
      }
    } catch (error) {
      console.error('Failed to regenerate API key:', error);
      toast.error('Failed to generate API key');
    } finally {
      setIsGenerating(false);
    }
  };

  if (isLoading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-4"></div>
          <div className="h-10 bg-gray-200 dark:bg-gray-700 rounded mb-4"></div>
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
        </div>
      </div>
    );
  }

  if (!hasAccess) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            API Access
          </h3>
          <div className="px-2 py-1 bg-amber-100 dark:bg-amber-900 text-amber-800 dark:text-amber-200 text-xs font-medium rounded">
            Subscription Required
          </div>
        </div>

        <div className="text-center py-8">
          <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
            <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
            </svg>
          </div>
          <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            API Access Locked
          </h4>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            Subscribe to get your API key and start making requests to our API.
          </p>
          <button
            onClick={() => window.location.href = '/pricing'}
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
          >
            View Pricing Plans
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          API Key Management
        </h3>
        <div className="flex items-center space-x-2">
          <div className="px-2 py-1 bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 text-xs font-medium rounded capitalize">
            {tier} Plan
          </div>
          {maskedKey && (
            <div className="px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs font-medium rounded">
              Active
            </div>
          )}
        </div>
      </div>

      <div className="space-y-4">
        {/* API Key Display */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Your API Key
          </label>
          <div className="flex items-center space-x-2">
            <div className="flex-1 min-w-0 px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg font-mono text-sm overflow-hidden">
              <div className="truncate">
                {showKey ? apiKey || maskedKey : maskedKey}
              </div>
            </div>
            <button
              onClick={() => setShowKey(!showKey)}
              className="flex-shrink-0 p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
              title={showKey ? "Hide key" : "Show key"}
              disabled={!apiKey}
            >
              {showKey ? (
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                </svg>
              ) : (
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
              )}
            </button>
          </div>
          {!apiKey && (
            <p className="text-xs text-amber-600 dark:text-amber-400 mt-1">
              🔒 For security, existing API keys cannot be displayed. Regenerate to get a new key.
            </p>
          )}
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-3">
          <button
            onClick={handleCopyKey}
            disabled={!apiKey}
            className="flex-1 flex items-center justify-center px-4 py-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
            </svg>
            {apiKey ? 'Copy Key' : 'Regenerate to Copy'}
          </button>
          
          <button
            onClick={handleRegenerateKey}
            disabled={isGenerating}
            className="flex-1 flex items-center justify-center px-4 py-2 bg-red-100 hover:bg-red-200 dark:bg-red-900 dark:hover:bg-red-800 text-red-700 dark:text-red-300 rounded-lg transition-colors disabled:opacity-50"
          >
            {isGenerating ? (
              <>
                <svg className="animate-spin w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Generating...
              </>
            ) : (
              <>
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                {maskedKey ? 'Regenerate' : 'Generate Key'}
              </>
            )}
          </button>
        </div>

        {/* API Key Statistics */}
        {(lastUsed || usageCount > 0) && (
          <div className="grid grid-cols-2 gap-4 mb-4">
            <div className="p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
              <p className="text-sm font-medium text-blue-800 dark:text-blue-200">Last Used</p>
              <p className="text-xs text-blue-600 dark:text-blue-400">
                {lastUsed ? new Date(lastUsed).toLocaleDateString() : 'Never'}
              </p>
            </div>
            <div className="p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
              <p className="text-sm font-medium text-green-800 dark:text-green-200">Total Requests</p>
              <p className="text-xs text-green-600 dark:text-green-400">{usageCount.toLocaleString()}</p>
            </div>
          </div>
        )}

        {/* Security Warning */}
        <div className="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg mb-4">
          <div className="flex">
            <svg className="w-5 h-5 text-red-400 mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            <div className="text-sm text-red-800 dark:text-red-200">
              <p className="font-semibold mb-2">🔒 Critical Security Guidelines</p>
              <ul className="space-y-1 text-xs">
                <li>• Never share your API key in public repositories</li>
                <li>• Store keys in environment variables, not in code</li>
                <li>• Regenerate immediately if compromised</li>
                <li>• Monitor usage for suspicious activity</li>
                <li>• Use HTTPS only for all API requests</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Rate Limiting Info */}
        <div className="p-3 bg-gray-50 dark:bg-gray-700/50 border border-gray-200 dark:border-gray-600 rounded-lg">
          <div className="flex items-center">
            <svg className="w-4 h-4 text-gray-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <div className="text-xs text-gray-600 dark:text-gray-400">
              <p><strong>Rate Limits:</strong> 1000 requests/hour • Burst: 100 requests/minute</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ApiKeyManager;
