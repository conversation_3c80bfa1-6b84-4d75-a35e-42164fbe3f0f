"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import apiClient from "@/libs/api";
import { toast } from "react-hot-toast";

const ApiKeyManager = () => {
  const { data: session } = useSession();
  const [showKey, setShowKey] = useState(false);
  const [isRegenerating, setIsRegenerating] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [apiKey, setApiKey] = useState("");
  const [maskedKey, setMaskedKey] = useState("");
  const [lastUsed, setLastUsed] = useState(null);
  const [usageCount, setUsageCount] = useState(0);

  // Fetch API key securely from server
  useEffect(() => {
    const fetchApiKey = async () => {
      if (!session?.user?.id) return;

      try {
        setIsLoading(true);
        const response = await apiClient.get('/api/user/api-key');

        if (response.success) {
          setApiKey(response.apiKey);
          setMaskedKey(maskApiKey(response.apiKey));
          setLastUsed(response.lastUsed);
          setUsageCount(response.usageCount || 0);
        }
      } catch (error) {
        console.error('Failed to fetch API key:', error);
        toast.error('Failed to load API key');
      } finally {
        setIsLoading(false);
      }
    };

    fetchApiKey();
  }, [session]);

  // Securely mask API key
  const maskApiKey = (key) => {
    if (!key || key.length < 8) return "••••••••••••••••••••••••••••••••";
    const prefix = key.substring(0, 8);
    const suffix = key.substring(key.length - 4);
    const masked = "••••••••••••••••••••";
    return `${prefix}${masked}${suffix}`;
  };

  const handleCopyKey = async () => {
    if (!apiKey) {
      toast.error('No API key available');
      return;
    }

    try {
      await navigator.clipboard.writeText(apiKey);
      toast.success('API key copied to clipboard');

      // Log the copy action for security audit
      await apiClient.post('/api/user/api-key/audit', {
        action: 'copy',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Failed to copy API key:', error);
      toast.error('Failed to copy API key');
    }
  };

  const handleRegenerateKey = async () => {
    if (!confirm('Are you sure you want to regenerate your API key? This will invalidate the current key and may break existing integrations.')) {
      return;
    }

    try {
      setIsRegenerating(true);
      const response = await apiClient.post('/api/user/api-key/regenerate');

      if (response.success) {
        setApiKey(response.apiKey);
        setMaskedKey(maskApiKey(response.apiKey));
        setLastUsed(null);
        setUsageCount(0);
        toast.success('API key regenerated successfully');

        // Show the new key temporarily
        setShowKey(true);
        setTimeout(() => setShowKey(false), 10000); // Hide after 10 seconds
      }
    } catch (error) {
      console.error('Failed to regenerate API key:', error);
      toast.error('Failed to regenerate API key');
    } finally {
      setIsRegenerating(false);
    }
  };

  if (isLoading) {
    return (
      <div className="bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-xl border border-gray-200 dark:border-gray-700 p-6 shadow-lg">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded mb-4"></div>
          <div className="h-10 bg-gray-200 dark:bg-gray-700 rounded mb-4"></div>
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-xl border border-gray-200 dark:border-gray-700 p-6 shadow-lg">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          API Key Management
        </h3>
        <div className="p-2 bg-gradient-to-r from-purple-500 to-blue-500 rounded-lg text-white">
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-3a1 1 0 011-1h2.586l6.243-6.243A6 6 0 0121 9z" />
          </svg>
        </div>
      </div>

      <div className="space-y-4">
        {/* API Key Display */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Your API Key
          </label>
          <div className="flex items-center space-x-2">
            <div className="flex-1 min-w-0 px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg font-mono text-sm overflow-hidden">
              <div className="truncate">
                {showKey ? apiKey : maskedKey}
              </div>
            </div>
            <button
              onClick={() => setShowKey(!showKey)}
              className="flex-shrink-0 p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
              title={showKey ? "Hide key" : "Show key"}
            >
              {showKey ? (
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                </svg>
              ) : (
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
              )}
            </button>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-3">
          <button
            onClick={handleCopyKey}
            className="flex-1 flex items-center justify-center px-4 py-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg transition-colors"
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
            </svg>
            Copy Key
          </button>
          
          <button
            onClick={handleRegenerateKey}
            disabled={isRegenerating}
            className="flex-1 flex items-center justify-center px-4 py-2 bg-red-100 hover:bg-red-200 dark:bg-red-900/30 dark:hover:bg-red-900/50 text-red-700 dark:text-red-300 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isRegenerating ? (
              <>
                <svg className="w-4 h-4 mr-2 animate-spin" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Regenerating...
              </>
            ) : (
              <>
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                Regenerate
              </>
            )}
          </button>
        </div>

        {/* API Key Statistics */}
        {(lastUsed || usageCount > 0) && (
          <div className="grid grid-cols-2 gap-4 mb-4">
            <div className="p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
              <p className="text-sm font-medium text-blue-800 dark:text-blue-200">Last Used</p>
              <p className="text-xs text-blue-600 dark:text-blue-400">
                {lastUsed ? new Date(lastUsed).toLocaleDateString() : 'Never'}
              </p>
            </div>
            <div className="p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
              <p className="text-sm font-medium text-green-800 dark:text-green-200">Total Requests</p>
              <p className="text-xs text-green-600 dark:text-green-400">{usageCount.toLocaleString()}</p>
            </div>
          </div>
        )}

        {/* Security Warning */}
        <div className="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg mb-4">
          <div className="flex">
            <svg className="w-5 h-5 text-red-400 mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            <div className="text-sm text-red-800 dark:text-red-200">
              <p className="font-semibold mb-2">🔒 Critical Security Guidelines</p>
              <ul className="space-y-1 text-xs">
                <li>• Never share your API key in public repositories</li>
                <li>• Store keys in environment variables, not in code</li>
                <li>• Regenerate immediately if compromised</li>
                <li>• Monitor usage for suspicious activity</li>
                <li>• Use HTTPS only for all API requests</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Rate Limiting Info */}
        <div className="p-3 bg-gray-50 dark:bg-gray-700/50 border border-gray-200 dark:border-gray-600 rounded-lg">
          <div className="flex items-center">
            <svg className="w-4 h-4 text-gray-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <div className="text-xs text-gray-600 dark:text-gray-400">
              <p><strong>Rate Limits:</strong> 1000 requests/hour • Burst: 100 requests/minute</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ApiKeyManager;
