// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("POSTGRE_DB")
}

// NextAuth.js required models
model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model User {
  id            String    @id @default(cuid())
  name          String?
  email         String?   @unique
  emailVerified DateTime?
  image         String?

  // Security: Enhanced user fields
  role          String    @default("USER") // USER, ADMIN, SUPER_ADMIN
  isActive      Boolean   @default(true)
  lastLoginAt   DateTime?
  loginAttempts Int       @default(0)
  lockedUntil   DateTime?



  // Security tracking
  passwordChangedAt DateTime?
  twoFactorEnabled  Boolean   @default(false)
  twoFactorSecret   String?

  // API Integration: Link to API database user
  apiUserId     String?   // User ID in the API database
  apiUserEmail  String?   // Email used in API database (for sync verification)

  // Stripe
  customerId    String?   // Stripe customer ID
  priceId       String?   // Stripe price ID
  hasAccess     Boolean   @default(false)

  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  accounts  Account[]
  sessions  Session[]
  leads     Lead[]

  @@map("users")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verificationtokens")
}

// Application specific models
model Lead {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String?
  source    String?  // Where the lead came from
  ipAddress String?  // For security tracking
  userAgent String?  // For security tracking
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  userId String?
  user   User?   @relation(fields: [userId], references: [id], onDelete: SetNull)

  @@map("leads")
}


