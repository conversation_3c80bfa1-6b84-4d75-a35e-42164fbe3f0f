# 🚀 Production Deployment Guide

This guide explains how to deploy the database changes and security enhancements to your production server.

## 📋 What Changed

### New Database Tables Added
- **`audit_logs`** - Comprehensive audit logging for security events
- **`security_events`** - Security incident tracking and monitoring
- **`rate_limits`** - Distributed rate limiting support

### Enhanced Existing Tables
- **`users`** - Added security fields (role, login attempts, API key management, 2FA support)
- **`leads`** - Added security tracking fields (IP address, user agent)

### New Security Features
- API key hashing and secure storage
- Comprehensive audit logging
- Security event monitoring
- Rate limiting infrastructure
- Enhanced authentication security

## 🔧 Automated Deployment (Recommended)

### Option 1: Automatic Deployment with Build
The easiest way is to use the automated deployment script that runs during build:

```bash
# On your production server
git pull origin main
pnpm install
pnpm run build  # This automatically runs database deployment
pnpm start
```

The `postbuild` script automatically runs `pnpm run db:deploy` which safely applies all database changes.

### Option 2: Complete Production Deployment
For a comprehensive deployment with all checks:

```bash
# On your production server
git pull origin main
pnpm install
pnpm run deploy:production
```

This runs the complete deployment process including:
- Environment validation
- Database backup verification
- Database migration
- Application build
- Health checks
- Deployment logging

## 🛠️ Manual Deployment (If Needed)

If you prefer to run the database migration manually:

### Step 1: Backup Your Database
```bash
# Create a backup before making changes
pg_dump $POSTGRE_DB > backup_$(date +%Y%m%d_%H%M%S).sql
```

### Step 2: Run Database Migration
```bash
# On your production server
cd /path/to/your/nextjs-frontend
pnpm run db:deploy
```

### Step 3: Build and Restart
```bash
pnpm run build
pnpm start
# or if using PM2:
pm2 restart nextjs-frontend
```

## 📊 What the Migration Does

### Safe Migration Process
1. **Checks existing database structure**
2. **Identifies missing tables/columns**
3. **Applies only necessary changes**
4. **Preserves all existing data**
5. **Logs the migration process**

### New Tables Created
```sql
-- Audit logging table
CREATE TABLE "audit_logs" (
  "id" TEXT PRIMARY KEY,
  "userId" TEXT,
  "action" TEXT NOT NULL,
  "details" JSONB,
  "ipAddress" TEXT,
  "userAgent" TEXT,
  "success" BOOLEAN DEFAULT true,
  "createdAt" TIMESTAMP DEFAULT now()
);

-- Security events table
CREATE TABLE "security_events" (
  "id" TEXT PRIMARY KEY,
  "type" TEXT NOT NULL,
  "severity" TEXT NOT NULL,
  "description" TEXT NOT NULL,
  "userId" TEXT,
  "metadata" JSONB,
  "resolved" BOOLEAN DEFAULT false,
  "createdAt" TIMESTAMP DEFAULT now()
);

-- Rate limiting table
CREATE TABLE "rate_limits" (
  "id" TEXT PRIMARY KEY,
  "key" TEXT UNIQUE NOT NULL,
  "count" INTEGER DEFAULT 1,
  "resetAt" TIMESTAMP NOT NULL,
  "createdAt" TIMESTAMP DEFAULT now()
);
```

### Enhanced User Table
```sql
-- New security fields added to users table
ALTER TABLE "users" ADD COLUMN "role" TEXT DEFAULT 'USER';
ALTER TABLE "users" ADD COLUMN "isActive" BOOLEAN DEFAULT true;
ALTER TABLE "users" ADD COLUMN "lastLoginAt" TIMESTAMP;
ALTER TABLE "users" ADD COLUMN "loginAttempts" INTEGER DEFAULT 0;
ALTER TABLE "users" ADD COLUMN "lockedUntil" TIMESTAMP;
ALTER TABLE "users" ADD COLUMN "apiKey" TEXT;
ALTER TABLE "users" ADD COLUMN "apiKeyCreatedAt" TIMESTAMP;
ALTER TABLE "users" ADD COLUMN "apiKeyLastUsed" TIMESTAMP;
ALTER TABLE "users" ADD COLUMN "apiKeyUsageCount" INTEGER DEFAULT 0;
ALTER TABLE "users" ADD COLUMN "twoFactorEnabled" BOOLEAN DEFAULT false;
ALTER TABLE "users" ADD COLUMN "twoFactorSecret" TEXT;
```

## ✅ Verification Steps

After deployment, verify everything is working:

### 1. Check Database Schema
```bash
pnpm run db:studio
```
Verify all new tables are present:
- audit_logs
- security_events  
- rate_limits

### 2. Test Application
- Visit your application
- Try logging in
- Check that the "Get Started" button works
- Verify dashboard functionality

### 3. Monitor Logs
```bash
# Check application logs
tail -f logs/output.log

# Check for any errors
tail -f logs/error.log
```

### 4. Security Dashboard
Visit `/dashboard/security` to see the new security monitoring features.

## 🔄 Rollback Plan (If Needed)

If something goes wrong, you can rollback:

### 1. Restore Database
```bash
# Restore from backup
psql $POSTGRE_DB < backup_YYYYMMDD_HHMMSS.sql
```

### 2. Revert Code
```bash
git checkout previous-working-commit
pnpm install
pnpm run build
pnpm start
```

## 📞 Support

If you encounter any issues:

1. **Check the deployment logs** for specific error messages
2. **Verify environment variables** are correctly set
3. **Ensure database connectivity** from the server
4. **Check Redis connectivity** if using caching

## 🎯 Benefits After Deployment

Once deployed, you'll have:

- ✅ **Bank-level security** with comprehensive audit logging
- ✅ **Real-time security monitoring** and threat detection
- ✅ **Enhanced user management** with role-based access
- ✅ **API key security** with proper hashing and tracking
- ✅ **Rate limiting** to prevent abuse
- ✅ **Security dashboard** for monitoring
- ✅ **Automated security alerts** for suspicious activity

## 📈 Next Steps

After successful deployment:

1. **Monitor the security dashboard** for any alerts
2. **Review audit logs** regularly
3. **Set up automated backups** if not already configured
4. **Configure security alerts** for your team
5. **Test all functionality** thoroughly

---

**Note:** The automated deployment scripts are designed to be safe and non-destructive. They only add new tables and columns without modifying existing data.
