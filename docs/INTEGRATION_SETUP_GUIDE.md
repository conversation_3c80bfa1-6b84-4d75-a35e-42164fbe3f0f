# 🚀 Integration Setup Guide

This guide walks you through setting up the secure integration between your Frontend NextJS app and API app.

## 📋 Prerequisites

1. **Frontend NextJS app** (current workspace) - running and functional
2. **API app** (separate repository) - with its own database
3. **Two PostgreSQL databases** - one for each app
4. **Redis** (optional but recommended for caching)

## 🔧 Step 1: Environment Configuration

### Frontend App (.env)
```bash
# Frontend Database
POSTGRE_DB=postgresql://username:password@localhost:5432/frontend_db

# API Database (for secure server-side access only)
POSTGRE_DB_API=postgresql://username:password@localhost:5432/api_db

# Internal API Security
API_INTERNAL_SECRET=your_strong_secret_here_min_32_chars
API_BASE_URL=http://localhost:3001

# Redis (optional)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# Existing variables...
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your_nextauth_secret
# ... other existing env vars
```

### API App (.env)
```bash
# API Database
DATABASE_URL=postgresql://username:password@localhost:5432/api_db

# Internal API Security (same secret as frontend)
API_INTERNAL_SECRET=your_strong_secret_here_min_32_chars

# ... other API app env vars
```

## 🗄️ Step 2: Database Schema Updates

### Frontend Database Migration
```bash
# In your frontend app directory
npx prisma db push
```

This will add the new user linking fields (`apiUserId`, `apiUserEmail`) to your frontend database.

### API Database Schema (Required in API App)
Your API app needs these models in its database schema:

```javascript
// API Database Schema (add to your API app's Prisma schema)
model User {
  id               String    @id @default(cuid())
  email            String    @unique
  name             String?
  frontendUserId   String?   // Link to frontend user
  tier             String    @default("free")
  creditsRemaining Int       @default(0)
  isActive         Boolean   @default(true)
  lastApiUsage     DateTime?
  createdAt        DateTime  @default(now())
  updatedAt        DateTime  @updatedAt

  apiKeys          ApiKey[]
  usageLogs        UsageLog[]
  auditLogs        AuditLog[]

  @@map("users")
}

model ApiKey {
  id           String    @id @default(cuid())
  userId       String
  keyHash      String    // SHA-256 hash of the API key
  keyPreview   String    // First 12 + last 4 characters
  isActive     Boolean   @default(true)
  usageCount   Int       @default(0)
  lastUsedAt   DateTime?
  createdAt    DateTime  @default(now())

  user         User      @relation(fields: [userId], references: [id])

  @@map("api_keys")
}

model UsageLog {
  id           String    @id @default(cuid())
  userId       String
  endpoint     String
  method       String
  creditsUsed  Int       @default(1)
  responseTime Int?
  statusCode   Int?
  createdAt    DateTime  @default(now())

  user         User      @relation(fields: [userId], references: [id])

  @@map("usage_logs")
}

model AuditLog {
  id        String   @id @default(cuid())
  userId    String?
  action    String
  details   Json?
  ipAddress String?
  userAgent String?
  success   Boolean  @default(true)
  createdAt DateTime @default(now())

  user      User?    @relation(fields: [userId], references: [id])

  @@map("audit_logs")
}
```

## 🔗 Step 3: Test the Integration

### 1. Start Both Applications
```bash
# Terminal 1: Frontend app
cd /path/to/frontend-app
pnpm dev

# Terminal 2: API app  
cd /path/to/api-app
npm start  # or your API app's start command
```

### 2. Test User Sync
1. Sign in to your frontend app
2. Visit the dashboard
3. The system should automatically sync your user to the API database

### 3. Test API Key Generation
1. Ensure you have an active subscription (`hasAccess = true`)
2. Click "Generate API Key" in the dashboard
3. Verify the key is created in the API database

### 4. Verify Database Linking
```sql
-- Check frontend database
SELECT id, email, "apiUserId", "hasAccess" FROM users WHERE email = '<EMAIL>';

-- Check API database (if you have access)
SELECT id, email, "frontendUserId", tier FROM users WHERE email = '<EMAIL>';
```

## 🔍 Step 4: Troubleshooting

### Common Issues

#### 1. "API database connection failed"
- Check `POSTGRE_DB_API` connection string
- Ensure API database is running and accessible
- Verify database credentials

#### 2. "User not found in API database"
- Check if user sync completed successfully
- Verify `apiUserId` field in frontend user record
- Try manual sync via webhook endpoint

#### 3. "API key generation requires subscription"
- Ensure user has `hasAccess = true` in frontend database
- Check Stripe subscription status
- Verify subscription sync is working

#### 4. "Internal API secret mismatch"
- Ensure `API_INTERNAL_SECRET` is identical in both apps
- Check for trailing spaces or encoding issues
- Regenerate secret if needed

### Debug Commands

```bash
# Test API database connection
node -e "require('./libs/api-database.js').checkApiDatabaseConnection().then(console.log)"

# Check user sync status
psql $POSTGRE_DB -c "SELECT id, email, \"apiUserId\", \"hasAccess\" FROM users LIMIT 5;"

# Test internal API communication
curl -X POST http://localhost:3000/api/webhook/subscription-sync \
  -H "Authorization: Bearer $API_INTERNAL_SECRET" \
  -H "Content-Type: application/json" \
  -d '{"userId":"user_id_here","action":"user_sync"}'
```

## 🛡️ Step 5: Security Verification

### 1. Verify Server-Side Only Access
- Confirm `libs/api-database.js` is never imported in client components
- Check that API database credentials are not exposed to client

### 2. Test Rate Limiting
- Try making multiple rapid requests to API endpoints
- Verify rate limiting is working correctly

### 3. Audit Logging
- Check that actions are logged in both databases
- Verify sensitive operations are properly audited

## 📊 Step 6: Monitoring Setup

### 1. Database Monitoring
- Monitor connection counts to both databases
- Set up alerts for failed sync operations
- Track API key usage patterns

### 2. Error Monitoring
- Monitor API endpoint error rates
- Set up alerts for authentication failures
- Track subscription sync failures

### 3. Performance Monitoring
- Monitor API response times
- Track database query performance
- Monitor Redis cache hit rates (if using)

## 🎯 Next Steps

### 1. API App Integration
You'll need to update your API app to:
- Validate API keys against the API database
- Log usage to the `usage_logs` table
- Implement rate limiting based on user tiers
- Send usage data back to the frontend

### 2. Enhanced Features
Consider implementing:
- Real-time usage dashboards
- API key rotation schedules
- Advanced analytics and reporting
- Multi-tier subscription support

### 3. Production Deployment
- Set up proper database backups
- Configure SSL/TLS for all connections
- Implement proper monitoring and alerting
- Set up automated deployment pipelines

## ✅ Success Criteria

Your integration is working correctly when:

- ✅ Users can sign in to the frontend app
- ✅ Users with subscriptions can generate API keys
- ✅ API keys are stored securely in the API database
- ✅ Usage statistics are displayed in the frontend dashboard
- ✅ Subscription changes sync between databases
- ✅ All actions are properly audited
- ✅ Rate limiting is enforced
- ✅ No sensitive data is exposed to the client

## 📚 Additional Resources

- [Database Integration Architecture](./DATABASE_INTEGRATION_ARCHITECTURE.md)
- [Security Implementation](./SECURITY_IMPLEMENTATION.md)
- [Production Deployment Guide](./PRODUCTION_DEPLOYMENT_GUIDE.md)

---

**Need Help?** Check the troubleshooting section above or review the detailed architecture documentation for more information.
