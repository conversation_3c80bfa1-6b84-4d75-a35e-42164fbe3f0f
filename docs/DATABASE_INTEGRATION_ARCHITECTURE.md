# 🏗️ Database Integration Architecture

This document explains the secure integration architecture between the Frontend NextJS app and the API app, maintaining database separation while enabling proper functionality.

## 🎯 Architecture Overview

### Two-Database Security Model

```
┌─────────────────────────────────────────────────────────────────┐
│                    FRONTEND NEXTJS APP                         │
│  ┌─────────────────────────────────────────────────────────┐    │
│  │              FRONTEND DATABASE                          │    │
│  │              (POSTGRE_DB)                              │    │
│  │                                                        │    │
│  │  • User accounts & authentication                     │    │
│  │  • NextAuth.js sessions                               │    │
│  │  • Stripe payments & subscriptions                    │    │
│  │  • User profiles & settings                           │    │
│  │  • Frontend audit logs                                │    │
│  │  • User linking (apiUserId field)                     │    │
│  └─────────────────────────────────────────────────────────┘    │
│                                                                 │
│  ┌─────────────────────────────────────────────────────────┐    │
│  │              SECURE API ROUTES                          │    │
│  │                                                        │    │
│  │  • /api/user/api-key (GET/POST)                       │    │
│  │  • /api/user/usage (GET)                              │    │
│  │  • /api/webhook/subscription-sync (POST)              │    │
│  │                                                        │    │
│  │  Uses ApiDatabaseService for secure communication     │    │
│  └─────────────────────────────────────────────────────────┘    │
└─────────────────────────────────────────────────────────────────┘
                                    │
                                    │ Secure Internal API
                                    │ (API_INTERNAL_SECRET)
                                    ▼
┌─────────────────────────────────────────────────────────────────┐
│                      API APP                                    │
│  ┌─────────────────────────────────────────────────────────┐    │
│  │                API DATABASE                             │    │
│  │              (POSTGRE_DB_API)                          │    │
│  │                                                        │    │
│  │  • API keys & authentication                          │    │
│  │  • Usage tracking & analytics                         │    │
│  │  • Rate limiting data                                 │    │
│  │  • API audit logs                                     │    │
│  │  • User tiers & credits                               │    │
│  │  • Frontend user linking (frontendUserId field)       │    │
│  └─────────────────────────────────────────────────────────┘    │
└─────────────────────────────────────────────────────────────────┘
```

## 🔗 User Linking System

### Frontend User Model
```javascript
// Frontend Database (POSTGRE_DB)
model User {
  id            String    @id @default(cuid())
  email         String?   @unique
  name          String?
  
  // API Integration: Link to API database user
  apiUserId     String?   // User ID in the API database
  apiUserEmail  String?   // Email used in API database (for sync verification)
  
  // Stripe & Subscription
  customerId    String?   // Stripe customer ID
  priceId       String?   // Stripe price ID
  hasAccess     Boolean   @default(false)
  
  // ... other fields
}
```

### API User Model (Expected in API Database)
```javascript
// API Database (POSTGRE_DB_API)
model User {
  id               String    @id @default(cuid())
  email            String    @unique
  name             String?
  
  // Frontend Integration: Link to frontend user
  frontendUserId   String?   // User ID in the frontend database
  
  // API Specific
  tier             String    @default("free") // free, starter, pro, enterprise
  creditsRemaining Int       @default(0)
  isActive         Boolean   @default(true)
  lastApiUsage     DateTime?
  
  // Relations
  apiKeys          ApiKey[]
  usageLogs        UsageLog[]
  auditLogs        AuditLog[]
}

model ApiKey {
  id           String    @id @default(cuid())
  userId       String
  keyHash      String    // Hashed API key for security
  keyPreview   String    // First 12 + last 4 characters for display
  isActive     Boolean   @default(true)
  usageCount   Int       @default(0)
  lastUsedAt   DateTime?
  createdAt    DateTime  @default(now())
  
  user         User      @relation(fields: [userId], references: [id])
}
```

## 🔐 Security Implementation

### 1. Database Separation
- **Frontend DB**: Handles user authentication, payments, and frontend-specific data
- **API DB**: Handles API keys, usage tracking, and API-specific functionality
- **No Direct Access**: Frontend never directly accesses API database with Prisma

### 2. Secure Communication
- **Internal API Secret**: All communication protected with `API_INTERNAL_SECRET`
- **Server-Side Only**: API database client only used in server-side API routes
- **Rate Limiting**: All endpoints have appropriate rate limiting
- **Audit Logging**: All actions logged in both databases

### 3. API Key Security
- **Hashed Storage**: API keys stored as SHA-256 hashes in API database
- **Preview Only**: Only key previews returned for existing keys
- **Full Key Once**: Complete key only returned during generation
- **Secure Transmission**: Keys transmitted over HTTPS only

## 🔄 Data Flow Examples

### 1. User Registration & Subscription
```
1. User signs up → Frontend DB (User created)
2. User subscribes → Stripe webhook → Frontend DB (hasAccess = true)
3. User visits dashboard → API route syncs user → API DB (User created with frontendUserId)
4. Frontend DB updated with apiUserId for future reference
```

### 2. API Key Generation
```
1. User clicks "Generate API Key" → Frontend API route
2. Route checks subscription status in Frontend DB
3. Route calls ApiDatabaseService.generateApiKey()
4. New API key created in API DB with hashed storage
5. Full key returned to user (one time only)
6. Action logged in both databases
```

### 3. Usage Tracking
```
1. API request made with API key → API App
2. API App validates key against API DB
3. Usage logged in API DB
4. Frontend dashboard requests usage → Frontend API route
5. Route calls ApiDatabaseService.getUserUsageStats()
6. Usage data returned to frontend
```

## 📡 API Endpoints

### Frontend API Routes

#### `/api/user/api-key` (GET)
- **Purpose**: Get user's API key information
- **Security**: Requires authentication, rate limited
- **Returns**: Key preview, usage stats, tier info
- **Database**: Reads from both Frontend and API databases

#### `/api/user/api-key` (POST)
- **Purpose**: Generate new API key
- **Security**: Requires active subscription, rate limited (3/hour)
- **Returns**: Full API key (one time only)
- **Database**: Creates record in API database

#### `/api/user/usage` (GET)
- **Purpose**: Get usage statistics
- **Security**: Requires authentication, rate limited
- **Returns**: Credits used, requests made, tier limits
- **Database**: Reads from API database

#### `/api/webhook/subscription-sync` (POST)
- **Purpose**: Sync subscription changes with API database
- **Security**: Requires internal API secret
- **Triggers**: Stripe webhooks, manual sync requests
- **Database**: Updates both databases

## 🛠️ Implementation Files

### Core Integration
- `libs/api-database.js` - Secure API database service
- `app/api/user/api-key/route.js` - API key management
- `app/api/user/usage/route.js` - Usage statistics
- `app/api/webhook/subscription-sync/route.js` - Subscription sync

### Frontend Components
- `components/dashboard/ApiKeyManager.js` - API key management UI
- `app/dashboard/page.js` - Main dashboard with API key component

### Configuration
- `.env.example` - Environment variables for both databases
- `prisma/schema.prisma` - Frontend database schema with linking fields

## 🔧 Environment Variables

```bash
# Frontend Database
POSTGRE_DB=postgresql://username:password@localhost:5432/frontend_db

# API Database (for secure server-side access only)
POSTGRE_DB_API=postgresql://username:password@localhost:5432/api_db

# Internal API Security
API_INTERNAL_SECRET=your_internal_api_secret_here
API_BASE_URL=http://localhost:3001

# Redis (optional, for caching)
REDIS_HOST=localhost
REDIS_PORT=6379
```

## 🚀 Deployment Considerations

### 1. Database Migrations
- Frontend DB: Use `prisma migrate deploy` for frontend schema
- API DB: Handle separately in API app deployment
- User linking: Automatic sync on first dashboard visit

### 2. Security
- Ensure `API_INTERNAL_SECRET` is strong and shared between apps
- Use HTTPS in production for all communication
- Regular security audits of both databases

### 3. Monitoring
- Monitor API key usage patterns
- Track sync failures between databases
- Alert on suspicious activity

## 🎯 Benefits of This Architecture

1. **Security Isolation**: Sensitive API data separated from user accounts
2. **Scalability**: Each database can be optimized for its specific use case
3. **Maintainability**: Clear separation of concerns
4. **Flexibility**: Can evolve each system independently
5. **Compliance**: Easier to meet security and compliance requirements

## 🔍 Troubleshooting

### Common Issues
1. **User not synced**: Check `apiUserId` field in frontend user
2. **API key not found**: Verify user has active subscription
3. **Sync failures**: Check `API_INTERNAL_SECRET` configuration
4. **Rate limiting**: Monitor rate limit logs in both databases

### Debug Commands
```bash
# Check user sync status
SELECT id, email, "apiUserId", "hasAccess" FROM users WHERE email = '<EMAIL>';

# Check API database connection
node -e "require('./libs/api-database.js').checkApiDatabaseConnection().then(console.log)"
```

This architecture provides a secure, scalable foundation for integrating the frontend and API applications while maintaining the security benefits of database separation.
